"""
配置管理模块
"""

import os
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 阿里百炼API配置
    dashscope_api_key: str = Field(..., env="DASHSCOPE_API_KEY")
    dashscope_model: str = Field("paraformer-realtime-v1", env="DASHSCOPE_MODEL")
    
    # 服务配置
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    debug: bool = Field(True, env="DEBUG")
    
    # 文件上传配置
    max_file_size: int = Field(10485760, env="MAX_FILE_SIZE")  # 10MB
    upload_dir: str = Field("./uploads", env="UPLOAD_DIR")
    temp_dir: str = Field("./temp", env="TEMP_DIR")
    
    # 语音识别配置
    default_language: str = Field("zh", env="DEFAULT_LANGUAGE")
    confidence_threshold: float = Field(0.5, env="CONFIDENCE_THRESHOLD")
    supported_formats: List[str] = Field(
        default=["wav", "mp3", "m4a", "flac", "aac", "ogg", "webm", "amr", "opus", "wma"],
        env="SUPPORTED_FORMATS"
    )

    # FFmpeg配置
    ffmpeg_path: str = Field("", env="FFMPEG_PATH")
    ffprobe_path: str = Field("", env="FFPROBE_PATH")
    
    # 日志配置
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("./logs/speech_service.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# AI服务平台配置文件

# 应用配置
app:
  name: "AI服务平台"
  version: "1.0.0"
  description: "AI服务集成平台，支持语音识别等多种AI功能"
  debug: true

# 服务配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  timeout: 30

# 语音识别配置
speech:
  # 阿里百炼配置
  dashscope:
    api_key: "${DASHSCOPE_API_KEY}"
    model: "paraformer-realtime-v1"
    timeout: 30
  
  # 支持的音频格式
  supported_formats:
    - wav
    - mp3
    - m4a
    - flac
    - aac
    - ogg
    - webm
    - amr
    - opus
    - wma
  
  # 音频处理配置
  audio:
    max_file_size: 10485760  # 10MB
    sample_rate: 16000
    channels: 1
    default_language: "zh"
    confidence_threshold: 0.5

# FFmpeg配置
ffmpeg:
  path: "${FFMPEG_PATH}"
  probe_path: "${FFPROBE_PATH}"
  timeout: 30

# 文件处理配置
files:
  upload_dir: "./uploads"
  temp_dir: "./temp"
  max_file_size: 10485760  # 10MB
  cleanup_interval: 3600   # 1小时
  max_age_hours: 24        # 24小时后清理

# 日志配置
logging:
  level: "INFO"
  file: "./logs/ai_service.log"
  max_size: 10485760  # 10MB
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 安全配置
security:
  cors:
    origins:
      - "*"
    methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
    headers:
      - "*"
  
  api_keys:
    header: "X-API-Key"
    keys: []  # 在环境变量中配置

# 性能配置
performance:
  max_concurrent_requests: 10
  request_timeout: 30
  worker_timeout: 30

# 监控配置
monitoring:
  health_check:
    enabled: true
    interval: 30
  
  metrics:
    enabled: true
    endpoint: "/metrics"
  
  alerts:
    cpu_threshold: 80
    memory_threshold: 80
    disk_threshold: 80

# 缓存配置（预留）
cache:
  enabled: false
  type: "memory"  # memory, redis
  ttl: 3600

# 数据库配置（预留）
database:
  enabled: false
  type: "sqlite"  # sqlite, postgresql, mysql
  url: "sqlite:///./ai_service.db"

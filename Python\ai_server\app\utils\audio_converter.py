"""
音频格式转换辅助模块
"""

import os
import tempfile
import subprocess
from typing import Optional, Tuple
from pathlib import Path
import logging

from pydub import AudioSegment
from pydub.utils import which

from ..config import get_settings


class AudioConverter:
    """音频格式转换器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        self._setup_ffmpeg()
    
    def _setup_ffmpeg(self):
        """设置FFmpeg路径"""
        try:
            # 首先尝试使用配置文件中的路径
            if self.settings.ffmpeg_path and self.settings.ffprobe_path:
                ffmpeg_path = self.settings.ffmpeg_path
                ffprobe_path = self.settings.ffprobe_path
                
                if os.path.exists(ffmpeg_path) and os.path.exists(ffprobe_path):
                    self.logger.info(f"使用配置的FFmpeg路径: {ffmpeg_path}")
                    
                    # 设置pydub的FFmpeg路径
                    AudioSegment.converter = ffmpeg_path
                    AudioSegment.ffmpeg = ffmpeg_path
                    AudioSegment.ffprobe = ffprobe_path
                    
                    # 设置环境变量
                    current_path = os.environ.get('PATH', '')
                    ffmpeg_dir = os.path.dirname(ffmpeg_path)
                    if ffmpeg_dir not in current_path:
                        os.environ['PATH'] = ffmpeg_dir + os.pathsep + current_path
                        self.logger.info(f"添加FFmpeg目录到PATH: {ffmpeg_dir}")
                    
                    # 测试FFmpeg
                    if self._test_ffmpeg(ffmpeg_path):
                        return
            
            # 回退到系统PATH中的FFmpeg
            ffmpeg_path = which("ffmpeg")
            ffprobe_path = which("ffprobe")
            
            if ffmpeg_path and ffprobe_path:
                self.logger.info(f"使用系统PATH中的FFmpeg: {ffmpeg_path}")
                return
            
            self.logger.warning("未找到FFmpeg，某些音频格式可能不支持")
            
        except Exception as e:
            self.logger.error(f"设置FFmpeg失败: {e}")
    
    def _test_ffmpeg(self, ffmpeg_path: str) -> bool:
        """测试FFmpeg是否可用"""
        try:
            result = subprocess.run(
                [ffmpeg_path, "-version"], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            if result.returncode == 0:
                self.logger.info("FFmpeg测试成功")
                return True
            else:
                self.logger.warning(f"FFmpeg测试失败: {result.stderr}")
                return False
        except Exception as e:
            self.logger.warning(f"FFmpeg测试异常: {e}")
            return False
    
    def convert_to_wav(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        将音频文件转换为WAV格式
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径，如果为None则自动生成
            
        Returns:
            转换后的WAV文件路径
        """
        try:
            # 检查输入文件
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"输入文件不存在: {input_path}")
            
            file_size = os.path.getsize(input_path)
            if file_size == 0:
                raise ValueError("输入文件为空")
            elif file_size < 1000:  # 小于1KB
                raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏")
            
            # 生成输出路径
            if output_path is None:
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    output_path = temp_file.name
            
            self.logger.info(f"开始转换音频: {input_path} -> {output_path}")
            
            # 尝试使用pydub转换
            try:
                audio = AudioSegment.from_file(input_path)
                
                # 转换为16kHz单声道WAV
                audio = audio.set_frame_rate(16000).set_channels(1)
                
                # 导出为WAV
                audio.export(output_path, format="wav")
                
                # 验证输出文件
                output_size = os.path.getsize(output_path)
                if output_size == 0:
                    raise ValueError("转换后的文件为空")
                
                self.logger.info(f"音频转换成功: {output_size} bytes")
                return output_path
                
            except Exception as e:
                self.logger.error(f"pydub转换失败: {e}")
                
                # 如果pydub失败，尝试直接使用FFmpeg
                return self._convert_with_ffmpeg(input_path, output_path)
                
        except Exception as e:
            self.logger.error(f"音频转换失败: {e}")
            # 清理可能创建的输出文件
            if output_path and os.path.exists(output_path):
                try:
                    os.unlink(output_path)
                except:
                    pass
            raise
    
    def _convert_with_ffmpeg(self, input_path: str, output_path: str) -> str:
        """使用FFmpeg直接转换音频"""
        try:
            ffmpeg_path = self.settings.ffmpeg_path or "ffmpeg"
            
            cmd = [
                ffmpeg_path,
                "-i", input_path,
                "-ar", "16000",  # 采样率16kHz
                "-ac", "1",      # 单声道
                "-y",            # 覆盖输出文件
                output_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg转换失败: {result.stderr}")
            
            # 验证输出文件
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                raise ValueError("FFmpeg转换后的文件为空")
            
            self.logger.info("FFmpeg转换成功")
            return output_path
            
        except Exception as e:
            self.logger.error(f"FFmpeg转换失败: {e}")
            raise
    
    def get_audio_info(self, file_path: str) -> dict:
        """
        获取音频文件信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            包含音频信息的字典
        """
        try:
            audio = AudioSegment.from_file(file_path)
            
            return {
                "duration": len(audio) / 1000.0,  # 时长（秒）
                "frame_rate": audio.frame_rate,   # 采样率
                "channels": audio.channels,       # 声道数
                "sample_width": audio.sample_width,  # 采样位深
                "file_size": os.path.getsize(file_path)  # 文件大小
            }
            
        except Exception as e:
            self.logger.error(f"获取音频信息失败: {e}")
            return {
                "duration": 0.0,
                "frame_rate": 0,
                "channels": 0,
                "sample_width": 0,
                "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
            }
    
    def is_supported_format(self, file_path: str) -> bool:
        """检查是否为支持的音频格式"""
        try:
            file_ext = Path(file_path).suffix.lower().lstrip('.')
            return file_ext in self.settings.supported_formats
        except:
            return False
    
    def cleanup_temp_file(self, file_path: str):
        """清理临时文件"""
        try:
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)
                self.logger.debug(f"清理临时文件: {file_path}")
        except Exception as e:
            self.logger.warning(f"清理临时文件失败: {e}")

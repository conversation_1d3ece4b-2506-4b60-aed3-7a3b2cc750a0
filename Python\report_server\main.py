"""
语音识别服务主入口
集成阿里百炼Paraformer SDK，提供HTTP API接口
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv

from src.speech_recognizer import SpeechRecognizer
from src.config import Settings
from src.models import SpeechRecognitionResponse, HealthResponse
from src.utils import setup_logging, create_directories

# 加载环境变量
load_dotenv()

# 全局变量
speech_recognizer: SpeechRecognizer = None
settings = Settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global speech_recognizer
    
    # 启动时初始化
    setup_logging(settings.log_level, settings.log_file)
    create_directories([settings.upload_dir, settings.temp_dir, os.path.dirname(settings.log_file)])
    
    speech_recognizer = SpeechRecognizer(settings)
    await speech_recognizer.initialize()
    
    logging.info("语音识别服务启动完成")
    
    yield
    
    # 关闭时清理
    if speech_recognizer:
        await speech_recognizer.cleanup()
    logging.info("语音识别服务关闭完成")


# 创建FastAPI应用
app = FastAPI(
    title="语音识别服务",
    description="基于阿里百炼Paraformer SDK的语音识别服务",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/recognize", response_model=SpeechRecognitionResponse)
async def recognize_speech(audio: UploadFile = File(...)):
    """
    语音识别接口
    
    Args:
        audio: 上传的音频文件
        
    Returns:
        SpeechRecognitionResponse: 识别结果
    """
    try:
        # 验证文件
        if not audio.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件大小
        if audio.size and audio.size > settings.max_file_size:
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制（{settings.max_file_size / 1024 / 1024:.1f}MB）"
            )
        
        # 检查文件格式
        file_extension = os.path.splitext(audio.filename)[1].lower().lstrip('.')
        if file_extension not in settings.supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的音频格式，支持的格式：{', '.join(settings.supported_formats)}"
            )
        
        # 执行语音识别
        result = await speech_recognizer.recognize(audio)
        
        return SpeechRecognitionResponse(
            success=True,
            text=result.text,
            confidence=result.confidence,
            duration=result.duration,
            language=result.language
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"语音识别失败: {str(e)}", exc_info=True)
        return SpeechRecognitionResponse(
            success=False,
            error=f"语音识别失败: {str(e)}"
        )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """
    健康检查接口
    
    Returns:
        HealthResponse: 服务状态信息
    """
    try:
        # 检查语音识别器状态
        is_ready = speech_recognizer and await speech_recognizer.is_ready()
        
        return HealthResponse(
            status="healthy" if is_ready else "unhealthy",
            version="1.0.0",
            timestamp=None,  # 会自动设置为当前时间
            details={
                "speech_recognizer": "ready" if is_ready else "not ready",
                "api_key_configured": bool(settings.dashscope_api_key),
                "upload_dir": settings.upload_dir,
                "temp_dir": settings.temp_dir
            }
        )
        
    except Exception as e:
        logging.error(f"健康检查失败: {str(e)}", exc_info=True)
        return HealthResponse(
            status="unhealthy",
            version="1.0.0",
            details={"error": str(e)}
        )


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logging.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "服务内部错误",
            "detail": str(exc) if settings.debug else "请联系管理员"
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )

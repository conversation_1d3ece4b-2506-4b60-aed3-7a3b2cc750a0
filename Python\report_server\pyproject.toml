[project]
name = "report_server"
version = "1.0.0"
description = "语音识别服务，集成阿里百炼Paraformer SDK"
authors = [
    {name = "CRM Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "httpx>=0.25.0",
    "dashscope>=1.14.0",
    "librosa>=0.10.1",
    "soundfile>=0.12.1",
    "numpy>=1.24.0",
    "python-dotenv>=1.0.0",
    "pydub>=0.25.1",
    "ffmpeg-python>=0.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]

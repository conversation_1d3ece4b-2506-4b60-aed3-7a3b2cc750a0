"""
语音识别器模块
集成阿里百炼Paraformer SDK
"""

import os
import logging
import asyncio
import tempfile
import ssl
from typing import Optional, Dict, Any, List
from pathlib import Path
import librosa
import soundfile as sf
from fastapi import UploadFile
import dashscope
from dashscope.audio.asr import Recognition, RecognitionCallback, RecognitionResult
from http import HTTPStatus
from pydub import AudioSegment
from pydub.utils import which
import subprocess

from .config import Settings
from .models import SpeechRecognitionResult, AudioFileInfo
from .utils import save_upload_file, get_temp_file_path, cleanup_file, get_file_extension


class SpeechRecognizer:
    """语音识别器"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self._initialized = False
    
    async def initialize(self):
        """初始化语音识别器"""
        try:
            # 设置阿里百炼API密钥
            dashscope.api_key = self.settings.dashscope_api_key

            # 设置FFmpeg路径
            self._setup_ffmpeg()

            # 测试API连接
            await self._test_api_connection()

            self._initialized = True
            self.logger.info(f"语音识别器初始化成功，使用模型: {self.settings.dashscope_model}")
            
        except Exception as e:
            self.logger.error(f"语音识别器初始化失败: {e}")
            raise
    
    def _setup_ffmpeg(self):
        """设置FFmpeg路径"""
        try:
            # 首先尝试使用配置文件中的路径
            if self.settings.ffmpeg_path and self.settings.ffprobe_path:
                ffmpeg_path = self.settings.ffmpeg_path
                ffprobe_path = self.settings.ffprobe_path

                if os.path.exists(ffmpeg_path) and os.path.exists(ffprobe_path):
                    self.logger.info(f"使用配置的FFmpeg路径: {ffmpeg_path}")
                    self.logger.info(f"使用配置的FFprobe路径: {ffprobe_path}")

                    # 设置pydub的FFmpeg路径
                    AudioSegment.converter = ffmpeg_path
                    AudioSegment.ffmpeg = ffmpeg_path
                    AudioSegment.ffprobe = ffprobe_path

                    # 设置环境变量，确保pydub能找到工具
                    current_path = os.environ.get('PATH', '')
                    ffmpeg_dir = os.path.dirname(ffmpeg_path)
                    if ffmpeg_dir not in current_path:
                        os.environ['PATH'] = ffmpeg_dir + os.pathsep + current_path
                        self.logger.info(f"添加FFmpeg目录到PATH: {ffmpeg_dir}")

                    # 测试FFmpeg是否工作
                    try:
                        result = subprocess.run([ffmpeg_path, "-version"],
                                              capture_output=True, text=True, timeout=5)
                        if result.returncode == 0:
                            self.logger.info("FFmpeg测试成功")
                            return
                        else:
                            self.logger.warning(f"FFmpeg测试失败: {result.stderr}")
                    except Exception as test_error:
                        self.logger.warning(f"FFmpeg测试异常: {test_error}")
                else:
                    self.logger.warning(f"配置的FFmpeg路径不存在: {ffmpeg_path}, {ffprobe_path}")

            # 如果配置文件中没有路径或路径无效，尝试常见路径
            common_paths = [
                r"D:\ffmpeg\bin\ffmpeg.exe",
                r"C:\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            ]

            for ffmpeg_path in common_paths:
                if os.path.exists(ffmpeg_path):
                    ffprobe_path = ffmpeg_path.replace("ffmpeg.exe", "ffprobe.exe")
                    if os.path.exists(ffprobe_path):
                        self.logger.info(f"找到FFmpeg: {ffmpeg_path}")
                        self.logger.info(f"找到FFprobe: {ffprobe_path}")

                        # 设置pydub的FFmpeg路径
                        AudioSegment.converter = ffmpeg_path
                        AudioSegment.ffmpeg = ffmpeg_path
                        AudioSegment.ffprobe = ffprobe_path

                        # 设置环境变量
                        current_path = os.environ.get('PATH', '')
                        ffmpeg_dir = os.path.dirname(ffmpeg_path)
                        if ffmpeg_dir not in current_path:
                            os.environ['PATH'] = ffmpeg_dir + os.pathsep + current_path
                            self.logger.info(f"添加FFmpeg目录到PATH: {ffmpeg_dir}")

                        return

            # 尝试系统PATH中的FFmpeg
            ffmpeg_path = which("ffmpeg")
            ffprobe_path = which("ffprobe")

            if ffmpeg_path and ffprobe_path:
                self.logger.info(f"使用系统PATH中的FFmpeg: {ffmpeg_path}")
                return

            self.logger.warning("未找到FFmpeg，WebM格式支持可能受限")

        except Exception as e:
            self.logger.error(f"设置FFmpeg失败: {e}")

    async def cleanup(self):
        """清理资源"""
        self._initialized = False
        self.logger.info("语音识别器清理完成")
    
    async def is_ready(self) -> bool:
        """检查识别器是否就绪"""
        return self._initialized
    
    async def recognize(self, audio_file: UploadFile) -> SpeechRecognitionResult:
        """
        执行语音识别
        
        Args:
            audio_file: 上传的音频文件
            
        Returns:
            SpeechRecognitionResult: 识别结果
        """
        if not self._initialized:
            raise RuntimeError("语音识别器未初始化")
        
        temp_file_path = None
        processed_file_path = None
        
        try:
            # 获取音频文件信息
            audio_info = await self._get_audio_info(audio_file)
            self.logger.info(f"处理音频文件: {audio_info.filename}, 大小: {audio_info.size}")
            
            # 保存上传的文件到临时目录
            file_extension = get_file_extension(audio_file.filename)
            temp_file_path = get_temp_file_path(f".{file_extension}")
            await save_upload_file(audio_file, temp_file_path)
            
            # 预处理音频文件
            processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
            
            # 调用阿里百炼API进行语音识别
            recognition_result = await self._call_dashscope_api(processed_file_path)
            
            # 获取音频时长
            duration = await self._get_audio_duration(processed_file_path)
            
            return SpeechRecognitionResult(
                text=recognition_result.get('text', ''),
                confidence=recognition_result.get('confidence', 0.0),
                duration=duration,
                language=self.settings.default_language
            )
            
        except Exception as e:
            self.logger.error(f"语音识别失败: {e}", exc_info=True)
            raise
        
        finally:
            # 清理临时文件
            if temp_file_path:
                cleanup_file(temp_file_path)
            if processed_file_path and processed_file_path != temp_file_path:
                cleanup_file(processed_file_path)
    
    async def _get_audio_info(self, audio_file: UploadFile) -> AudioFileInfo:
        """获取音频文件信息"""
        return AudioFileInfo(
            filename=audio_file.filename or "unknown",
            size=audio_file.size or 0,
            content_type=audio_file.content_type or "audio/unknown"
        )
    
    async def _preprocess_audio(self, file_path: str, audio_info: AudioFileInfo) -> str:
        """
        预处理音频文件
        - 转换格式为WAV
        - 调整采样率
        - 单声道转换
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"音频文件不存在: {file_path}")

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError(f"音频文件为空: {file_path}")
            elif file_size < 1000:  # 小于1KB
                raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。")

            self.logger.info(f"开始预处理音频文件: {file_path}, 大小: {file_size} bytes")

            # 读取音频文件
            try:
                audio_data, sample_rate = librosa.load(file_path, sr=None, mono=True)
            except Exception as e:
                self.logger.error(f"librosa读取音频失败: {e}")
                # 尝试使用soundfile直接读取
                try:
                    audio_data, sample_rate = sf.read(file_path)
                    if len(audio_data.shape) > 1:
                        audio_data = audio_data.mean(axis=1)  # 转为单声道
                except Exception as e2:
                    self.logger.error(f"soundfile读取音频也失败: {e2}")
                    # 尝试使用pydub处理WebM等格式
                    try:
                        self.logger.info(f"尝试使用pydub处理音频文件: {file_path}")

                        # 检测文件扩展名
                        file_ext = os.path.splitext(file_path)[1].lower()
                        self.logger.info(f"检测到文件格式: {file_ext}")

                        # 使用pydub加载音频（支持更多格式）
                        if file_ext == '.webm':
                            # 对于WebM格式，明确指定格式
                            audio_segment = AudioSegment.from_file(file_path, format="webm")
                        else:
                            # 让pydub自动检测格式
                            audio_segment = AudioSegment.from_file(file_path)

                        self.logger.info(f"pydub成功加载音频: 时长={len(audio_segment)}ms, 声道={audio_segment.channels}, 采样率={audio_segment.frame_rate}Hz")

                        # 转换为单声道
                        if audio_segment.channels > 1:
                            audio_segment = audio_segment.set_channels(1)

                        # 转换为WAV格式的临时文件
                        temp_wav_path = file_path.replace(os.path.splitext(file_path)[1], '_converted.wav')
                        audio_segment.export(temp_wav_path, format="wav", parameters=["-ar", "16000"])

                        # 使用librosa读取转换后的WAV文件
                        audio_data, sample_rate = librosa.load(temp_wav_path, sr=None, mono=True)

                        # 清理临时文件
                        if os.path.exists(temp_wav_path):
                            os.remove(temp_wav_path)

                        self.logger.info(f"pydub转换成功，音频长度: {len(audio_data)}, 采样率: {sample_rate}")

                    except Exception as e3:
                        self.logger.error(f"pydub处理音频也失败: {e3}")

                        # 检查文件大小，如果太小说明录音有问题
                        file_size = os.path.getsize(file_path)
                        if file_size < 1000:  # 小于1KB
                            raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏")

                        # 最后尝试：返回明确的错误信息
                        raise ValueError(f"WebM格式处理失败，需要安装FFmpeg。文件大小: {file_size} bytes。错误详情: librosa({e}), soundfile({e2}), pydub({e3})")

            # 检查音频数据是否有效
            if len(audio_data) == 0:
                raise ValueError("音频数据为空，可能是无效的音频文件")

            # 检查音频时长
            duration = len(audio_data) / sample_rate
            if duration < 0.1:  # 少于0.1秒
                raise ValueError(f"音频时长太短: {duration:.2f}秒，至少需要0.1秒")

            self.logger.info(f"音频信息: 时长={duration:.2f}秒, 原始采样率={sample_rate}Hz, 数据长度={len(audio_data)}")

            # 如果采样率不是16kHz，则重采样
            target_sample_rate = 16000
            if sample_rate != target_sample_rate:
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=target_sample_rate)
                sample_rate = target_sample_rate
                self.logger.info(f"重采样到 {target_sample_rate}Hz")

            # 保存为WAV格式
            processed_file_path = get_temp_file_path(".wav")
            sf.write(processed_file_path, audio_data, sample_rate)

            # 验证生成的文件
            processed_size = os.path.getsize(processed_file_path)
            self.logger.info(f"音频预处理完成: {processed_file_path}, 大小: {processed_size} bytes, 采样率: {sample_rate}Hz")

            return processed_file_path

        except Exception as e:
            self.logger.error(f"音频预处理失败: {e}")
            raise
    
    async def _call_dashscope_api(self, file_path: str) -> dict:
        """调用阿里百炼API进行语音识别"""
        try:
            # 使用阿里百炼的语音识别API
            recognition = Recognition(
                model=self.settings.dashscope_model,  # 使用配置的模型
                format='wav',
                sample_rate=16000,
                callback=None
            )

            self.logger.info(f"使用百炼模型: {self.settings.dashscope_model}")
            
            # 执行识别
            result = recognition.call(file_path)
            
            if result.status_code == 200:
                # 解析识别结果
                output = result.output
                self.logger.info(f"API返回的output结构: {output}")

                if output and 'sentence' in output:
                    sentence = output['sentence']

                    # 处理sentence可能是列表的情况
                    if isinstance(sentence, list) and len(sentence) > 0:
                        # 取第一个句子
                        sentence = sentence[0]

                    if isinstance(sentence, dict):
                        return {
                            'text': sentence.get('text', ''),
                            'confidence': sentence.get('confidence', 0.0)
                        }
                    else:
                        self.logger.warning(f"sentence格式不正确: {sentence}")
                        return {'text': '', 'confidence': 0.0}
                else:
                    self.logger.warning(f"output中没有sentence字段: {output}")
                    return {'text': '', 'confidence': 0.0}
            else:
                error_msg = f"API调用失败: {result.status_code}, {result.message}"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg)
                
        except Exception as e:
            self.logger.error(f"调用阿里百炼API失败: {e}")
            raise
    
    async def _get_audio_duration(self, file_path: str) -> float:
        """获取音频时长"""
        try:
            audio_data, sample_rate = librosa.load(file_path, sr=None)
            duration = len(audio_data) / sample_rate
            return duration
        except Exception as e:
            self.logger.warning(f"获取音频时长失败: {e}")
            return 0.0
    
    async def _test_api_connection(self):
        """测试API连接"""
        try:
            # 这里可以添加一个简单的API测试
            # 由于没有测试音频文件，我们只验证API密钥是否设置
            if not self.settings.dashscope_api_key:
                raise ValueError("阿里百炼API密钥未设置")
            
            self.logger.info("API连接测试通过")
            
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            raise

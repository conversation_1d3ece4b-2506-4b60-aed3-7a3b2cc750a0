# 语音识别服务

基于阿里百炼Paraformer SDK的语音识别服务，为CRM系统提供语音转文字功能。

## 功能特性

- 🎤 支持多种音频格式（WAV、MP3、M4A、FLAC、AAC、OGG、WEBM、AMR、OPUS、WMA）
- 🚀 基于阿里百炼Paraformer模型，识别准确率高
- 📊 提供识别置信度和音频时长信息
- 🔧 自动音频预处理（格式转换、采样率调整、单声道转换）
- 🌐 RESTful API接口，易于集成
- 📝 完整的日志记录和错误处理
- 🏥 健康检查接口

## 快速开始

### 1. 环境要求

- Python 3.9+
- uv 包管理器（推荐）

### 2. 安装和配置

#### 使用启动脚本（推荐）

```bash
# 运行uv专用启动脚本，会自动处理依赖和配置
python start_uv.py
```

#### 手动安装

```bash
# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync

# 复制配置文件
cp .env.example .env

# 编辑配置文件，设置API密钥
vim .env
```

### 3. 配置说明

编辑 `.env` 文件，设置以下配置：

```env
# 阿里百炼API配置
DASHSCOPE_API_KEY=your_api_key_here
DASHSCOPE_MODEL=paraformer-realtime-v1

# FFmpeg配置（用于WebM等格式支持）
# Windows路径示例
FFMPEG_PATH=D:\ffmpeg\bin\ffmpeg.exe
FFPROBE_PATH=D:\ffmpeg\bin\ffprobe.exe
# Linux/Mac路径示例
# FFMPEG_PATH=/usr/local/bin/ffmpeg
# FFPROBE_PATH=/usr/local/bin/ffprobe

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# 语音识别配置
DEFAULT_LANGUAGE=zh
CONFIDENCE_THRESHOLD=0.5

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/speech_service.log
```

#### 详细配置说明

##### 阿里百炼模型配置

| 模型名称 | 描述 | 适用场景 |
|---------|------|----------|
| `paraformer-realtime-v1` | 实时模型（推荐） | 实时语音识别，响应速度快 |
| `paraformer-v1` | 标准模型 | 高精度语音识别 |
| `paraformer-8k-v1` | 8k采样率模型 | 电话语音等低采样率场景 |
| `paraformer-mtl-v1` | 多任务学习模型 | 支持多种语音任务 |

##### FFmpeg安装说明

**Windows:**
1. 下载FFmpeg：https://ffmpeg.org/download.html
2. 解压到 `D:\ffmpeg\` 目录
3. 在.env中配置路径或添加到系统PATH

**Linux:**
```bash
sudo apt update
sudo apt install ffmpeg
```

**Mac:**
```bash
brew install ffmpeg
```

##### 配置优先级

**FFmpeg路径查找顺序：**
1. `.env`文件中的`FFMPEG_PATH`和`FFPROBE_PATH`
2. 常见安装路径（`D:\ffmpeg\bin\`, `C:\ffmpeg\bin\`等）
3. 系统环境变量PATH中的ffmpeg

### 4. 启动服务

```bash
# 使用uv运行
uv run python main.py

# 或直接运行
python main.py
```

服务启动后，访问 http://localhost:8000/docs 查看API文档。

### 5. 启动验证

服务启动时会显示配置信息，确认一切正常：

```log
2025-07-31 10:03:40,080 - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:03:40,081 - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:03:40,202 - FFmpeg测试成功
2025-07-31 10:03:40,203 - API连接测试通过
2025-07-31 10:03:40,203 - 语音识别器初始化成功，使用模型: paraformer-realtime-v1
2025-07-31 10:03:40,203 - 语音识别服务启动完成
```

如果看到以上日志，说明服务配置正确并成功启动。

### 6. 配置检查清单

在启动服务前，请确认以下配置：

- [ ] 已设置 `DASHSCOPE_API_KEY`
- [ ] 已选择合适的 `DASHSCOPE_MODEL`
- [ ] FFmpeg已安装并配置路径（用于WebM格式支持）
- [ ] 创建了必要的目录（uploads、temp、logs）
- [ ] 端口未被占用
- [ ] 文件权限正确设置

## API接口

### 语音识别

**POST** `/recognize`

上传音频文件进行语音识别。

**请求参数：**
- `audio`: 音频文件（multipart/form-data）

**响应示例：**
```json
{
  "success": true,
  "text": "这是识别出的文本内容",
  "confidence": 0.95,
  "duration": 3.2,
  "language": "zh-CN",
  "error": ""
}
```

### 健康检查

**GET** `/health`

检查服务状态。

**响应示例：**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00",
  "details": {
    "speech_recognizer": "ready",
    "api_key_configured": true,
    "upload_dir": "./uploads",
    "temp_dir": "./temp"
  }
}
```

## 支持的音频格式

- WAV
- MP3
- M4A
- FLAC
- AAC
- OGG
- WEBM
- AMR
- OPUS
- WMA

## 开发

### 项目结构

```text
speech_recognition_service/
├── main.py                 # 主入口文件
├── start.py               # 启动脚本
├── pyproject.toml         # 项目配置
├── .env.example           # 环境变量示例
├── README.md              # 说明文档
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── models.py          # 数据模型
│   ├── speech_recognizer.py  # 语音识别器
│   └── utils.py           # 工具函数
├── uploads/               # 上传文件目录
├── temp/                  # 临时文件目录
└── logs/                  # 日志目录
```

## 部署

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libsndfile1 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 复制项目文件
COPY . .

# 安装Python依赖
RUN uv sync --frozen

# 创建必要目录
RUN mkdir -p uploads temp logs

# 暴露端口
EXPOSE 8000

# 启动服务
CMD ["uv", "run", "python", "main.py"]
```

### 生产环境配置

1. 设置环境变量 `DEBUG=False`
2. 配置反向代理（如Nginx）
3. 使用进程管理器（如systemd、supervisor）
4. 配置日志轮转
5. 设置文件权限和安全策略

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的 `DASHSCOPE_API_KEY` 是否正确
   - 确认API密钥有效且有足够的配额

2. **FFmpeg相关错误**
   - 检查FFmpeg路径是否正确配置
   - 确认FFmpeg和FFprobe都已安装
   - 验证文件权限和可执行性
   - 查看启动日志确认FFmpeg测试是否成功

3. **模型加载错误**
   - 检查 `DASHSCOPE_MODEL` 配置是否正确
   - 确认API密钥支持该模型
   - 查看详细错误日志

4. **音频文件格式不支持**
   - 检查文件扩展名是否在支持列表中
   - 对于WebM格式，确保FFmpeg已正确配置
   - 尝试转换为WAV格式

5. **文件大小超限**
   - 检查文件大小是否超过 `MAX_FILE_SIZE` 设置
   - 可以在 `.env` 文件中调整限制

6. **服务无法启动**
   - 检查端口是否被占用
   - 查看日志文件获取详细错误信息
   - 确认所有必需的配置项都已设置

### 日志查看

```bash
# 查看实时日志
tail -f logs/speech_service.log

# 查看错误日志
grep ERROR logs/speech_service.log
```

# 应用基础配置
APP_NAME=AI服务平台
APP_VERSION=1.0.0
DEBUG=True

# 服务配置
HOST=0.0.0.0
PORT=8000

# 阿里百炼API配置
DASHSCOPE_API_KEY=sk-9f44f8543c584cc0874b064fc1f6784f
DASHSCOPE_MODEL=paraformer-v2

# FFmpeg配置
FFMPEG_PATH=D:\ffmpeg\bin\ffmpeg.exe
FFPROBE_PATH=D:\ffmpeg\bin\ffprobe.exe

# 文件处理配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# 语音识别配置
DEFAULT_LANGUAGE=zh
CONFIDENCE_THRESHOLD=0.5
SUPPORTED_FORMATS=["wav","mp3","m4a","flac","aac","ogg","webm","amr","opus","wma"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/ai_service.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# 安全配置
CORS_ORIGINS=["*"]
API_KEY_HEADER=X-API-Key
API_KEYS=[]

# 性能配置
WORKER_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=10

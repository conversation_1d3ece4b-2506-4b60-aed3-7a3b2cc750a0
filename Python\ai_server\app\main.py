"""
FastAPI应用主入口
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import time
import uvicorn

from .config import get_settings
from .utils import setup_logger
from .api import speech_router, health_router


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    # 获取配置
    settings = get_settings()
    
    # 设置日志
    setup_logger()
    
    # 创建FastAPI应用
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="AI服务集成平台，支持语音识别等多种AI功能",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None
    )
    
    # 添加中间件
    setup_middleware(app, settings)
    
    # 注册路由
    register_routes(app)
    
    # 添加事件处理器
    setup_event_handlers(app)
    
    return app


def setup_middleware(app: FastAPI, settings):
    """设置中间件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 信任主机中间件
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", settings.host]
        )
    
    # 请求处理时间中间件
    @app.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response


def register_routes(app: FastAPI):
    """注册路由"""
    
    # 注册API路由
    app.include_router(speech_router, prefix="/api/v1")
    app.include_router(health_router, prefix="/api/v1")
    
    # 根路径
    @app.get("/", summary="根路径", description="API根路径")
    async def root():
        settings = get_settings()
        return {
            "message": f"欢迎使用{settings.app_name}",
            "version": settings.app_version,
            "docs": "/docs" if settings.debug else "文档已禁用",
            "health": "/api/v1/health"
        }


def setup_event_handlers(app: FastAPI):
    """设置事件处理器"""
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        settings = get_settings()
        logger = setup_logger("startup")
        
        logger.info(f"🚀 {settings.app_name} v{settings.app_version} 启动中...")
        logger.info(f"📍 服务地址: http://{settings.host}:{settings.port}")
        logger.info(f"🔧 调试模式: {'开启' if settings.debug else '关闭'}")
        logger.info(f"🤖 语音识别模型: {settings.dashscope_model}")
        
        if settings.debug:
            logger.info(f"📚 API文档: http://{settings.host}:{settings.port}/docs")
        
        logger.info("✅ 应用启动完成")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        logger = setup_logger("shutdown")
        logger.info("🛑 应用正在关闭...")
        
        # 这里可以添加清理逻辑
        # 例如：关闭数据库连接、清理临时文件等
        
        logger.info("✅ 应用已安全关闭")


# 创建应用实例
app = create_app()


def main():
    """主函数"""
    settings = get_settings()
    
    # 启动服务
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()

"""
应用配置管理
"""

import os
from typing import List, Optional
from functools import lru_cache
from pathlib import Path
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = Field("AI服务平台", env="APP_NAME")
    app_version: str = Field("1.0.0", env="APP_VERSION")
    debug: bool = Field(True, env="DEBUG")
    
    # 服务配置
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    
    # 阿里百炼API配置
    dashscope_api_key: str = Field(..., env="DASHSCOPE_API_KEY")
    dashscope_model: str = Field("paraformer-realtime-v1", env="DASHSCOPE_MODEL")
    
    # FFmpeg配置
    ffmpeg_path: str = Field("", env="FFMPEG_PATH")
    ffprobe_path: str = Field("", env="FFPROBE_PATH")
    
    # 文件处理配置
    max_file_size: int = Field(10485760, env="MAX_FILE_SIZE")  # 10MB
    upload_dir: str = Field("./uploads", env="UPLOAD_DIR")
    temp_dir: str = Field("./temp", env="TEMP_DIR")
    
    # 语音识别配置
    default_language: str = Field("zh", env="DEFAULT_LANGUAGE")
    confidence_threshold: float = Field(0.5, env="CONFIDENCE_THRESHOLD")
    supported_formats: List[str] = Field(
        default=["wav", "mp3", "m4a", "flac", "aac", "ogg", "webm", "amr", "opus", "wma"],
        env="SUPPORTED_FORMATS"
    )
    
    # 日志配置
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("./logs/ai_service.log", env="LOG_FILE")
    log_max_size: int = Field(10485760, env="LOG_MAX_SIZE")  # 10MB
    log_backup_count: int = Field(5, env="LOG_BACKUP_COUNT")
    
    # 安全配置
    cors_origins: List[str] = Field(
        default=["*"], 
        env="CORS_ORIGINS"
    )
    api_key_header: str = Field("X-API-Key", env="API_KEY_HEADER")
    api_keys: List[str] = Field(default=[], env="API_KEYS")
    
    # 性能配置
    worker_timeout: int = Field(30, env="WORKER_TIMEOUT")
    max_concurrent_requests: int = Field(10, env="MAX_CONCURRENT_REQUESTS")
    
    class Config:
        # 使用绝对路径确保能找到.env文件
        env_file = Path(__file__).parent.parent.parent / ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.upload_dir,
            self.temp_dir,
            os.path.dirname(self.log_file)
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    settings = Settings()
    settings.create_directories()
    return settings

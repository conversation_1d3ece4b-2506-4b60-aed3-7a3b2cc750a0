"""
健康检查控制器
"""

import os
import psutil
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter
from fastapi.responses import JSONResponse

from ..config import get_settings
from ..utils import get_logger

# 创建路由器
router = APIRouter(prefix="/health", tags=["健康检查"])
logger = get_logger(__name__)


@router.get("/", summary="健康检查", description="检查服务健康状态")
async def health_check() -> JSONResponse:
    """基础健康检查"""
    try:
        settings = get_settings()
        
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "AI服务平台",
            "version": "1.0.0"
        }
        
        response_data = {
            "succeed": True,
            "data": health_data,
            "messages": [],
            "timestamp": None
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        error_response = {
            "succeed": False,
            "data": {"status": "unhealthy", "error": str(e)},
            "messages": [f"健康检查失败: {str(e)}"],
            "timestamp": None
        }
        return JSONResponse(content=error_response, status_code=503)


@router.get("/detailed", summary="详细健康检查", description="获取详细的系统健康信息")
async def detailed_health_check() -> JSONResponse:
    """详细健康检查"""
    try:
        settings = get_settings()
        
        # 系统信息
        system_info = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat()
        }
        
        # 目录检查
        directories = {
            "upload_dir": {
                "path": settings.upload_dir,
                "exists": os.path.exists(settings.upload_dir),
                "writable": os.access(settings.upload_dir, os.W_OK) if os.path.exists(settings.upload_dir) else False
            },
            "temp_dir": {
                "path": settings.temp_dir,
                "exists": os.path.exists(settings.temp_dir),
                "writable": os.access(settings.temp_dir, os.W_OK) if os.path.exists(settings.temp_dir) else False
            },
            "log_dir": {
                "path": os.path.dirname(settings.log_file),
                "exists": os.path.exists(os.path.dirname(settings.log_file)),
                "writable": os.access(os.path.dirname(settings.log_file), os.W_OK) if os.path.exists(os.path.dirname(settings.log_file)) else False
            }
        }
        
        # FFmpeg检查
        ffmpeg_status = {
            "configured": bool(settings.ffmpeg_path and settings.ffprobe_path),
            "ffmpeg_exists": os.path.exists(settings.ffmpeg_path) if settings.ffmpeg_path else False,
            "ffprobe_exists": os.path.exists(settings.ffprobe_path) if settings.ffprobe_path else False
        }
        
        # 配置检查
        config_status = {
            "dashscope_api_key": bool(settings.dashscope_api_key),
            "model": settings.dashscope_model,
            "debug_mode": settings.debug
        }
        
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "AI服务平台",
            "version": "1.0.0",
            "system": system_info,
            "directories": directories,
            "ffmpeg": ffmpeg_status,
            "config": config_status
        }
        
        # 检查是否有问题
        issues = []
        
        # 检查系统资源
        if system_info["cpu_percent"] > 90:
            issues.append("CPU使用率过高")
        if system_info["memory_percent"] > 90:
            issues.append("内存使用率过高")
        if system_info["disk_percent"] > 90:
            issues.append("磁盘使用率过高")
        
        # 检查目录
        for dir_name, dir_info in directories.items():
            if not dir_info["exists"]:
                issues.append(f"{dir_name}目录不存在")
            elif not dir_info["writable"]:
                issues.append(f"{dir_name}目录不可写")
        
        # 检查FFmpeg
        if ffmpeg_status["configured"]:
            if not ffmpeg_status["ffmpeg_exists"]:
                issues.append("FFmpeg可执行文件不存在")
            if not ffmpeg_status["ffprobe_exists"]:
                issues.append("FFprobe可执行文件不存在")
        
        # 检查配置
        if not config_status["dashscope_api_key"]:
            issues.append("阿里百炼API密钥未配置")
        
        if issues:
            health_data["status"] = "degraded"
            health_data["issues"] = issues
        
        response_data = {
            "succeed": True,
            "data": health_data,
            "messages": issues,
            "timestamp": None
        }
        
        status_code = 200 if health_data["status"] == "healthy" else 206
        return JSONResponse(content=response_data, status_code=status_code)
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        error_response = {
            "succeed": False,
            "data": {"status": "unhealthy", "error": str(e)},
            "messages": [f"详细健康检查失败: {str(e)}"],
            "timestamp": None
        }
        return JSONResponse(content=error_response, status_code=503)


@router.get("/ready", summary="就绪检查", description="检查服务是否就绪")
async def readiness_check() -> JSONResponse:
    """就绪检查"""
    try:
        settings = get_settings()
        
        # 检查关键配置
        ready = True
        issues = []
        
        if not settings.dashscope_api_key:
            ready = False
            issues.append("阿里百炼API密钥未配置")
        
        if not os.path.exists(settings.upload_dir):
            ready = False
            issues.append("上传目录不存在")
        
        if not os.path.exists(settings.temp_dir):
            ready = False
            issues.append("临时目录不存在")
        
        ready_data = {
            "ready": ready,
            "timestamp": datetime.now().isoformat(),
            "issues": issues
        }
        
        response_data = {
            "succeed": True,
            "data": ready_data,
            "messages": issues,
            "timestamp": None
        }
        
        status_code = 200 if ready else 503
        return JSONResponse(content=response_data, status_code=status_code)
        
    except Exception as e:
        logger.error(f"就绪检查失败: {e}")
        error_response = {
            "succeed": False,
            "data": {"ready": False, "error": str(e)},
            "messages": [f"就绪检查失败: {str(e)}"],
            "timestamp": None
        }
        return JSONResponse(content=error_response, status_code=503)

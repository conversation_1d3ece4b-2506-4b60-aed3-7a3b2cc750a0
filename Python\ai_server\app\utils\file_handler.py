"""
文件处理辅助模块
"""

import os
import tempfile
import hashlib
from typing import Optional, BinaryIO
from pathlib import Path
import logging

from fastapi import UploadFile

from ..config import get_settings


class FileHandler:
    """文件处理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
    
    def save_upload_file(self, upload_file: UploadFile, custom_filename: Optional[str] = None) -> str:
        """
        保存上传的文件
        
        Args:
            upload_file: FastAPI上传文件对象
            custom_filename: 自定义文件名
            
        Returns:
            保存后的文件路径
        """
        try:
            # 验证文件大小
            if hasattr(upload_file, 'size') and upload_file.size:
                if upload_file.size > self.settings.max_file_size:
                    raise ValueError(f"文件大小超过限制: {upload_file.size} > {self.settings.max_file_size}")
            
            # 生成文件名
            if custom_filename:
                filename = custom_filename
            else:
                # 使用原文件名或生成唯一文件名
                if upload_file.filename:
                    filename = self._generate_unique_filename(upload_file.filename)
                else:
                    filename = f"upload_{self._generate_hash()}.bin"
            
            # 保存文件
            file_path = os.path.join(self.settings.upload_dir, filename)
            
            with open(file_path, "wb") as buffer:
                content = upload_file.file.read()
                
                # 验证内容大小
                if len(content) > self.settings.max_file_size:
                    raise ValueError(f"文件内容大小超过限制: {len(content)} > {self.settings.max_file_size}")
                
                buffer.write(content)
            
            # 验证保存的文件
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                raise ValueError("文件保存失败或为空")
            
            self.logger.info(f"文件保存成功: {file_path} ({os.path.getsize(file_path)} bytes)")
            return file_path
            
        except Exception as e:
            self.logger.error(f"保存上传文件失败: {e}")
            raise
    
    def create_temp_file(self, suffix: str = "", prefix: str = "temp_") -> str:
        """
        创建临时文件
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            临时文件路径
        """
        try:
            with tempfile.NamedTemporaryFile(
                suffix=suffix,
                prefix=prefix,
                dir=self.settings.temp_dir,
                delete=False
            ) as temp_file:
                temp_path = temp_file.name
            
            self.logger.debug(f"创建临时文件: {temp_path}")
            return temp_path
            
        except Exception as e:
            self.logger.error(f"创建临时文件失败: {e}")
            raise
    
    def cleanup_file(self, file_path: str):
        """清理文件"""
        try:
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)
                self.logger.debug(f"清理文件: {file_path}")
        except Exception as e:
            self.logger.warning(f"清理文件失败: {e}")
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                return {"exists": False}
            
            stat = os.stat(file_path)
            path_obj = Path(file_path)
            
            return {
                "exists": True,
                "name": path_obj.name,
                "size": stat.st_size,
                "extension": path_obj.suffix.lower(),
                "created_time": stat.st_ctime,
                "modified_time": stat.st_mtime,
                "is_file": path_obj.is_file(),
                "is_dir": path_obj.is_dir()
            }
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {"exists": False, "error": str(e)}
    
    def validate_file_type(self, file_path: str, allowed_extensions: list) -> bool:
        """
        验证文件类型
        
        Args:
            file_path: 文件路径
            allowed_extensions: 允许的扩展名列表
            
        Returns:
            是否为允许的文件类型
        """
        try:
            file_ext = Path(file_path).suffix.lower().lstrip('.')
            return file_ext in [ext.lower().lstrip('.') for ext in allowed_extensions]
        except:
            return False
    
    def _generate_unique_filename(self, original_filename: str) -> str:
        """生成唯一文件名"""
        try:
            path_obj = Path(original_filename)
            name = path_obj.stem
            ext = path_obj.suffix
            
            # 生成时间戳和哈希
            import time
            timestamp = str(int(time.time()))
            hash_part = self._generate_hash()[:8]
            
            return f"{name}_{timestamp}_{hash_part}{ext}"
            
        except:
            # 如果处理失败，使用完全随机的文件名
            return f"file_{self._generate_hash()}.bin"
    
    def _generate_hash(self) -> str:
        """生成随机哈希"""
        import time
        import random
        
        content = f"{time.time()}_{random.random()}".encode()
        return hashlib.md5(content).hexdigest()
    
    def ensure_directory(self, directory: str):
        """确保目录存在"""
        try:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                self.logger.debug(f"创建目录: {directory}")
        except Exception as e:
            self.logger.error(f"创建目录失败: {e}")
            raise
    
    def get_directory_size(self, directory: str) -> int:
        """获取目录大小"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        except Exception as e:
            self.logger.error(f"获取目录大小失败: {e}")
            return 0
    
    def cleanup_old_files(self, directory: str, max_age_hours: int = 24):
        """清理旧文件"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_count = 0
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        self.cleanup_file(file_path)
                        cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个旧文件")
                
        except Exception as e:
            self.logger.error(f"清理旧文件失败: {e}")

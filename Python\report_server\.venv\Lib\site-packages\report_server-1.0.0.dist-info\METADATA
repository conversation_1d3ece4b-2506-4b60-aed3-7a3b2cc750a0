Metadata-Version: 2.4
Name: report_server
Version: 1.0.0
Summary: 语音识别服务，集成阿里百炼Paraformer SDK
Author-email: CRM Team <<EMAIL>>
Requires-Python: >=3.9
Requires-Dist: aiofiles>=23.2.1
Requires-Dist: dashscope>=1.14.0
Requires-Dist: fastapi>=0.104.0
Requires-Dist: ffmpeg-python>=0.2.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: librosa>=0.10.1
Requires-Dist: numpy>=1.24.0
Requires-Dist: pydantic-settings>=2.0.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydub>=0.25.1
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: soundfile>=0.12.1
Requires-Dist: uvicorn[standard]>=0.24.0
Provides-Extra: dev
Requires-Dist: black>=23.0.0; extra == 'dev'
Requires-Dist: flake8>=6.0.0; extra == 'dev'
Requires-Dist: isort>=5.12.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'dev'
Requires-Dist: pytest>=7.4.0; extra == 'dev'
Description-Content-Type: text/markdown

# 语音识别服务

基于阿里百炼Paraformer SDK的语音识别服务，为CRM系统提供语音转文字功能。

## 功能特性

- 🎤 支持多种音频格式（WAV、MP3、M4A、FLAC、AAC、OGG、WEBM、AMR、OPUS、WMA）
- 🚀 基于阿里百炼Paraformer模型，识别准确率高
- 📊 提供识别置信度和音频时长信息
- 🔧 自动音频预处理（格式转换、采样率调整、单声道转换）
- 🌐 RESTful API接口，易于集成
- 📝 完整的日志记录和错误处理
- 🏥 健康检查接口

## 快速开始

### 1. 环境要求

- Python 3.9+
- uv 包管理器（推荐）

### 2. 安装和配置

#### 使用启动脚本（推荐）

```bash
# 运行uv专用启动脚本，会自动处理依赖和配置
python start_uv.py
```

#### 手动安装

```bash
# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync

# 复制配置文件
cp .env.example .env

# 编辑配置文件，设置API密钥
vim .env
```

### 3. 配置说明

编辑 `.env` 文件，设置以下配置：

```env
# 阿里百炼API密钥（必需）
DASHSCOPE_API_KEY=your_api_key_here

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# 语音识别配置
DEFAULT_LANGUAGE=zh
CONFIDENCE_THRESHOLD=0.5

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/speech_service.log
```

### 4. 启动服务

```bash
# 使用uv运行
uv run python main.py

# 或直接运行
python main.py
```

服务启动后，访问 http://localhost:8000/docs 查看API文档。

## API接口

### 语音识别

**POST** `/recognize`

上传音频文件进行语音识别。

**请求参数：**
- `audio`: 音频文件（multipart/form-data）

**响应示例：**
```json
{
  "success": true,
  "text": "这是识别出的文本内容",
  "confidence": 0.95,
  "duration": 3.2,
  "language": "zh-CN",
  "error": ""
}
```

### 健康检查

**GET** `/health`

检查服务状态。

**响应示例：**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00",
  "details": {
    "speech_recognizer": "ready",
    "api_key_configured": true,
    "upload_dir": "./uploads",
    "temp_dir": "./temp"
  }
}
```

## 支持的音频格式

- WAV
- MP3
- M4A
- FLAC
- AAC
- OGG
- WEBM
- AMR
- OPUS
- WMA

## 开发

### 项目结构

```
speech_recognition_service/
├── main.py                 # 主入口文件
├── start.py               # 启动脚本
├── pyproject.toml         # 项目配置
├── .env.example           # 环境变量示例
├── README.md              # 说明文档
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── models.py          # 数据模型
│   ├── speech_recognizer.py  # 语音识别器
│   └── utils.py           # 工具函数
├── uploads/               # 上传文件目录
├── temp/                  # 临时文件目录
└── logs/                  # 日志目录
```

### 运行测试

```bash
# 运行uv专用测试脚本
python test_uv.py

# 或安装开发依赖后运行pytest
uv sync --dev
uv run pytest
```

### 代码格式化

```bash
# 格式化代码
uv run black .
uv run isort .

# 检查代码质量
uv run flake8
```

## 部署

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libsndfile1 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 复制项目文件
COPY . .

# 安装Python依赖
RUN uv sync --frozen

# 创建必要目录
RUN mkdir -p uploads temp logs

# 暴露端口
EXPOSE 8000

# 启动服务
CMD ["uv", "run", "python", "main.py"]
```

### 生产环境配置

1. 设置环境变量 `DEBUG=False`
2. 配置反向代理（如Nginx）
3. 使用进程管理器（如systemd、supervisor）
4. 配置日志轮转
5. 设置文件权限和安全策略

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的 `DASHSCOPE_API_KEY` 是否正确
   - 确认API密钥有效且有足够的配额

2. **音频文件格式不支持**
   - 检查文件扩展名是否在支持列表中
   - 尝试转换为WAV格式

3. **文件大小超限**
   - 检查文件大小是否超过 `MAX_FILE_SIZE` 设置
   - 可以在 `.env` 文件中调整限制

4. **服务无法启动**
   - 检查端口是否被占用
   - 查看日志文件获取详细错误信息

### 日志查看

```bash
# 查看实时日志
tail -f logs/speech_service.log

# 查看错误日志
grep ERROR logs/speech_service.log
```

## 许可证

本项目采用 MIT 许可证。

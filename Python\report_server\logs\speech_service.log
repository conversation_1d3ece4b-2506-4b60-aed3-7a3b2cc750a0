2025-07-30 10:24:05,738 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 10:24:05,739 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 10:24:05,739 - root - INFO - 语音识别服务启动完成
2025-07-30 10:34:36,183 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 10:34:36,200 - root - INFO - 语音识别服务关闭完成
2025-07-30 10:34:39,384 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 10:34:39,385 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 10:34:39,385 - root - INFO - 语音识别服务启动完成
2025-07-30 10:34:45,371 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 10:34:45,372 - root - INFO - 语音识别服务关闭完成
2025-07-30 10:34:47,199 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 10:34:47,200 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 10:34:47,200 - root - INFO - 语音识别服务启动完成
2025-07-30 10:34:49,206 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 10:34:49,206 - root - INFO - 语音识别服务关闭完成
2025-07-30 10:35:29,336 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 10:35:29,336 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 10:35:29,336 - root - INFO - 语音识别服务启动完成
2025-07-30 14:25:39,062 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 14:25:39,069 - root - INFO - 语音识别服务关闭完成
2025-07-30 14:25:45,513 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 14:25:45,513 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 14:25:45,513 - root - INFO - 语音识别服务启动完成
2025-07-30 14:39:47,739 - root - ERROR - 健康检查失败: 1 validation error for HealthResponse
timestamp
  Input should be a valid datetime [type=datetime_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 132, in health_check
    return HealthResponse(
        status="healthy" if is_ready else "unhealthy",
    ...<7 lines>...
        }
    )
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for HealthResponse
timestamp
  Input should be a valid datetime [type=datetime_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.11/v/datetime_type
2025-07-30 14:40:34,185 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 14:40:34,186 - root - INFO - 语音识别服务关闭完成
2025-07-30 14:40:41,620 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 14:40:41,621 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 14:40:41,621 - root - INFO - 语音识别服务启动完成
2025-07-30 15:10:40,166 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 1127
2025-07-30 15:11:21,523 - src.speech_recognizer - ERROR - 音频预处理失败: 
2025-07-30 15:11:21,913 - dashscope - ERROR - Request failed, request_id: 0b53c601a0e041b6b617cbeed9262cba, http_code: 44 error_name: NO_VALID_AUDIO_ERROR, error_message: NO_VALID_AUDIO_ERROR
2025-07-30 15:11:21,916 - dashscope - ERROR - {"status_code": 44, "request_id": "0b53c601a0e041b6b617cbeed9262cba", "code": "NO_VALID_AUDIO_ERROR", "message": "NO_VALID_AUDIO_ERROR", "output": null, "usage": null}
2025-07-30 15:11:21,917 - src.speech_recognizer - ERROR - API调用失败: 44, NO_VALID_AUDIO_ERROR
2025-07-30 15:11:21,917 - src.speech_recognizer - ERROR - 调用阿里百炼API失败: API调用失败: 44, NO_VALID_AUDIO_ERROR
2025-07-30 15:11:21,918 - src.speech_recognizer - ERROR - 语音识别失败: API调用失败: 44, NO_VALID_AUDIO_ERROR
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 84, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 172, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, NO_VALID_AUDIO_ERROR
2025-07-30 15:11:21,926 - root - ERROR - 语音识别失败: API调用失败: 44, NO_VALID_AUDIO_ERROR
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 84, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 172, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, NO_VALID_AUDIO_ERROR
2025-07-30 15:11:55,983 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 15:11:55,983 - root - INFO - 语音识别服务关闭完成
2025-07-30 15:45:51,307 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 15:45:51,307 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 15:45:51,308 - root - INFO - 语音识别服务启动完成
2025-07-30 15:55:33,151 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 63917
2025-07-30 15:55:33,186 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp33dj4d8u.webm, 大小: 63917 bytes
2025-07-30 15:55:37,401 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 15:55:37,403 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33dj4d8u.webm': Format not recognised.
2025-07-30 15:55:37,404 - src.speech_recognizer - ERROR - 音频预处理失败: 无法读取音频文件，可能格式不支持: 
2025-07-30 15:55:37,405 - src.speech_recognizer - ERROR - 语音识别失败: 无法读取音频文件，可能格式不支持: 
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 176, in load
    y, sr_native = __soundfile_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 209, in __soundfile_load
    context = sf.SoundFile(path)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33dj4d8u.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 136, in _preprocess_audio
    audio_data, sample_rate = librosa.load(file_path, sr=None, mono=True)
                              ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 184, in load
    y, sr_native = __audioread_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\util\decorators.py", line 63, in __wrapper
    return func(*args, **kwargs)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 240, in __audioread_load
    reader = audioread.audio_open(path)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\audioread\__init__.py", line 132, in audio_open
    raise NoBackendError()
audioread.exceptions.NoBackendError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 141, in _preprocess_audio
    audio_data, sample_rate = sf.read(file_path)
                              ~~~~~~~^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 305, in read
    with SoundFile(file, 'r', samplerate, channels,
         ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   subtype, endian, format, closefd) as f:
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33dj4d8u.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 81, in recognize
    processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 146, in _preprocess_audio
    raise ValueError(f"无法读取音频文件，可能格式不支持: {e}")
ValueError: 无法读取音频文件，可能格式不支持: 
2025-07-30 15:55:37,580 - root - ERROR - 语音识别失败: 无法读取音频文件，可能格式不支持: 
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 176, in load
    y, sr_native = __soundfile_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 209, in __soundfile_load
    context = sf.SoundFile(path)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33dj4d8u.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 136, in _preprocess_audio
    audio_data, sample_rate = librosa.load(file_path, sr=None, mono=True)
                              ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 184, in load
    y, sr_native = __audioread_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\util\decorators.py", line 63, in __wrapper
    return func(*args, **kwargs)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 240, in __audioread_load
    reader = audioread.audio_open(path)
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\audioread\__init__.py", line 132, in audio_open
    raise NoBackendError()
audioread.exceptions.NoBackendError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 141, in _preprocess_audio
    audio_data, sample_rate = sf.read(file_path)
                              ~~~~~~~^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 305, in read
    with SoundFile(file, 'r', samplerate, channels,
         ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   subtype, endian, format, closefd) as f:
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp33dj4d8u.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 81, in recognize
    processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 146, in _preprocess_audio
    raise ValueError(f"无法读取音频文件，可能格式不支持: {e}")
ValueError: 无法读取音频文件，可能格式不支持: 
2025-07-30 15:56:09,218 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 15:56:09,219 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:08:21,454 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:08:21,455 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:08:21,456 - root - INFO - 语音识别服务启动完成
2025-07-30 16:09:02,465 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:09:02,467 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:09:04,794 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:09:04,798 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:09:04,799 - root - INFO - 语音识别服务启动完成
2025-07-30 16:09:17,755 - src.speech_recognizer - INFO - 处理音频文件: tmpnw3hi5wq.wav, 大小: 88244
2025-07-30 16:09:17,764 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpw8tf_a2r.wav, 大小: 88244 bytes
2025-07-30 16:09:23,369 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=44100Hz, 数据长度=44100
2025-07-30 16:09:23,389 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 16:09:23,392 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpnynn8wg6.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:09:23,910 - src.speech_recognizer - ERROR - 调用阿里百炼API失败: 'list' object has no attribute 'get'
2025-07-30 16:09:23,910 - src.speech_recognizer - ERROR - 语音识别失败: 'list' object has no attribute 'get'
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 86, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 223, in _call_dashscope_api
    'text': sentence.get('text', ''),
            ^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'get'
2025-07-30 16:09:23,925 - root - ERROR - 语音识别失败: 'list' object has no attribute 'get'
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 86, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 223, in _call_dashscope_api
    'text': sentence.get('text', ''),
            ^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'get'
2025-07-30 16:09:51,399 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:09:51,399 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:09:54,260 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:09:54,260 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:09:54,260 - root - INFO - 语音识别服务启动完成
2025-07-30 16:16:09,649 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:16:09,650 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:16:09,650 - root - INFO - 语音识别服务启动完成
2025-07-30 16:16:53,266 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:16:53,266 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:16:55,550 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:16:55,550 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:16:55,551 - root - INFO - 语音识别服务启动完成
2025-07-30 16:17:34,004 - src.speech_recognizer - INFO - 处理音频文件: tmpjcysocw0.wav, 大小: 88244
2025-07-30 16:17:34,020 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp3ibg8wzk.wav, 大小: 88244 bytes
2025-07-30 16:17:39,024 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=44100Hz, 数据长度=44100
2025-07-30 16:17:39,032 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 16:17:39,038 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpfib8plb0.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:17:39,530 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 980, 'text': '嗯。', 'words': [{'begin_time': 0, 'end_time': 980, 'text': '嗯', 'punctuation': '。'}]}]}
2025-07-30 16:18:13,809 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:18:13,809 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:18:13,809 - root - INFO - 语音识别服务启动完成
2025-07-30 16:18:30,297 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:18:30,298 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:19:06,663 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 65849
2025-07-30 16:19:06,670 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpc21dxmp8.webm, 大小: 65849 bytes
2025-07-30 16:19:06,697 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 16:19:06,698 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpc21dxmp8.webm': Format not recognised.
2025-07-30 16:19:06,704 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpc21dxmp8.webm
2025-07-30 16:19:06,712 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 16:19:06,737 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 16:19:06,745 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 16:19:06,746 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 16:19:06,749 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 16:19:06,761 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp2rukcb8d.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:19:07,135 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 16:19:07,135 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 16:19:41,514 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:19:41,515 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:19:41,515 - root - INFO - 语音识别服务启动完成
2025-07-30 16:20:17,704 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 31073
2025-07-30 16:20:17,707 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpspev2_aa.webm, 大小: 31073 bytes
2025-07-30 16:20:17,724 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 16:20:17,724 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpspev2_aa.webm': Format not recognised.
2025-07-30 16:20:17,725 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpspev2_aa.webm
2025-07-30 16:20:17,725 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 16:20:17,733 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 16:20:17,734 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 16:20:17,734 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 16:20:17,735 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 16:20:17,739 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp1xze2h4u.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:20:18,026 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 16:20:18,027 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 16:21:03,637 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:21:03,637 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:21:03,735 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:21:03,738 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:21:07,988 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:21:07,989 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:21:07,989 - root - INFO - 语音识别服务启动完成
2025-07-30 16:21:08,802 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:21:08,803 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:21:08,803 - root - INFO - 语音识别服务启动完成
2025-07-30 16:21:49,784 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 50393
2025-07-30 16:21:49,790 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpk04t03la.webm, 大小: 50393 bytes
2025-07-30 16:21:53,030 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 16:21:53,031 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpk04t03la.webm': Format not recognised.
2025-07-30 16:21:53,031 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpk04t03la.webm
2025-07-30 16:21:53,031 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 16:21:53,038 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 16:21:53,044 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 16:21:53,045 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 16:21:53,046 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 16:21:53,052 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpn6xg78a_.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:21:53,444 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 16:21:53,445 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 16:22:11,407 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:22:11,409 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:28:14,978 - src.speech_recognizer - INFO - 处理音频文件: tmpp4v2qkc7.wav, 大小: 88244
2025-07-30 16:28:14,996 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp5rdnclhp.wav, 大小: 88244 bytes
2025-07-30 16:28:15,039 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=44100Hz, 数据长度=44100
2025-07-30 16:28:15,061 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 16:28:15,067 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp6l5d4t5s.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:28:15,574 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 980, 'text': '嗯。', 'words': [{'begin_time': 0, 'end_time': 980, 'text': '嗯', 'punctuation': '。'}]}]}
2025-07-30 16:29:48,518 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:29:48,519 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:30:10,966 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:30:10,967 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:30:10,967 - root - INFO - 语音识别服务启动完成
2025-07-30 16:30:34,948 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 81305
2025-07-30 16:30:34,957 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpysl3866c.webm, 大小: 81305 bytes
2025-07-30 16:30:38,587 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 16:30:38,587 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpysl3866c.webm': Format not recognised.
2025-07-30 16:30:38,588 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpysl3866c.webm
2025-07-30 16:30:38,588 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 16:30:38,594 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 16:30:38,594 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 16:30:38,595 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 16:30:38,595 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 16:30:38,600 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpidw9mjui.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:30:39,070 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 16:30:39,071 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 16:32:23,191 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 44597
2025-07-30 16:32:23,194 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpwm6fbpol.webm, 大小: 44597 bytes
2025-07-30 16:32:23,208 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 16:32:23,209 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwm6fbpol.webm': Format not recognised.
2025-07-30 16:32:23,209 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpwm6fbpol.webm
2025-07-30 16:32:23,210 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 16:32:23,218 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 16:32:23,219 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 16:32:23,220 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 16:32:23,220 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 16:32:23,223 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpogkj1mow.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:32:23,541 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 16:32:23,541 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 16:38:51,555 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:38:51,559 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:42:53,394 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:42:53,395 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:42:53,395 - root - INFO - 语音识别服务启动完成
2025-07-30 16:43:45,095 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 167279
2025-07-30 16:43:45,105 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpfrmujzws.webm, 大小: 167279 bytes
2025-07-30 16:43:48,477 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 16:43:48,478 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpfrmujzws.webm': Format not recognised.
2025-07-30 16:43:48,478 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpfrmujzws.webm
2025-07-30 16:43:48,479 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 16:43:48,486 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 16:43:48,486 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 16:43:48,486 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 16:43:48,487 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 16:43:48,495 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpzp_vbu4u.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:43:48,954 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 16:43:48,956 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 16:49:12,502 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:49:12,502 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:49:16,560 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:49:16,560 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:49:16,562 - root - INFO - 语音识别服务启动完成
2025-07-30 16:51:21,770 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:51:21,771 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:51:21,771 - root - INFO - 语音识别服务启动完成
2025-07-30 16:54:23,323 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:54:23,324 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:54:23,325 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:54:23,327 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:54:26,288 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:54:26,289 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:54:26,289 - root - INFO - 语音识别服务启动完成
2025-07-30 16:54:26,289 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:54:26,289 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:54:26,290 - root - INFO - 语音识别服务启动完成
2025-07-30 16:54:38,969 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:54:38,969 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:54:38,970 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:54:38,970 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:54:41,585 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:54:41,586 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:54:41,586 - root - INFO - 语音识别服务启动完成
2025-07-30 16:54:41,588 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:54:41,588 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:54:41,588 - root - INFO - 语音识别服务启动完成
2025-07-30 16:55:22,447 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:55:22,447 - root - INFO - 语音识别服务关闭完成
2025-07-30 16:56:50,168 - src.speech_recognizer - INFO - 处理音频文件: tmpzxhdx2fj.wav, 大小: 88244
2025-07-30 16:56:50,182 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpu_neh61t.wav, 大小: 88244 bytes
2025-07-30 16:56:55,503 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=44100Hz, 数据长度=44100
2025-07-30 16:56:55,511 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 16:56:55,515 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpczof526i.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:56:56,081 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 980, 'text': '嗯。', 'words': [{'begin_time': 0, 'end_time': 980, 'text': '嗯', 'punctuation': '。'}]}]}
2025-07-30 16:56:56,082 - src.speech_recognizer - INFO - 提取的文本: '嗯。', 置信度: 0.0
2025-07-30 16:56:56,082 - src.speech_recognizer - INFO - API调用返回结果: {'text': '嗯。', 'confidence': 0.0}
2025-07-30 16:56:56,083 - src.speech_recognizer - INFO - 最终识别结果 - 文本: '嗯。', 置信度: 0.0, 时长: 1.0s
2025-07-30 16:56:56,084 - root - INFO - 识别器返回结果: text='嗯。', confidence=0.0, duration=1.0, language='zh'
2025-07-30 16:56:56,093 - root - INFO - 最终API响应: {'success': True, 'text': '嗯。', 'confidence': 0.0, 'duration': 1.0, 'language': 'zh', 'error': ''}
2025-07-30 16:57:58,627 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 16:57:58,627 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 16:57:58,627 - root - INFO - 语音识别服务启动完成
2025-07-30 16:58:49,071 - src.speech_recognizer - INFO - 处理音频文件: tmp6nj40duw.wav, 大小: 88244
2025-07-30 16:58:49,076 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpch0d0g4k.wav, 大小: 88244 bytes
2025-07-30 16:58:49,086 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=44100Hz, 数据长度=44100
2025-07-30 16:58:49,089 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 16:58:49,092 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpv_j93dqw.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 16:58:49,511 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 980, 'text': '嗯。', 'words': [{'begin_time': 0, 'end_time': 980, 'text': '嗯', 'punctuation': '。'}]}]}
2025-07-30 16:58:49,512 - src.speech_recognizer - INFO - 提取的文本: '嗯。', 置信度: 0.0
2025-07-30 16:58:49,512 - src.speech_recognizer - INFO - API调用返回结果: {'text': '嗯。', 'confidence': 0.0}
2025-07-30 16:58:49,513 - src.speech_recognizer - INFO - 最终识别结果 - 文本: '嗯。', 置信度: 0.0, 时长: 1.0s
2025-07-30 16:58:49,517 - root - INFO - 识别器返回结果: text='嗯。', confidence=0.0, duration=1.0, language='zh'
2025-07-30 16:58:49,518 - root - INFO - 最终API响应: {'success': True, 'text': '嗯。', 'confidence': 0.0, 'duration': 1.0, 'language': 'zh', 'error': ''}
2025-07-30 16:59:44,514 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 16:59:44,514 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:04:13,524 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:04:13,524 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:04:13,524 - root - INFO - 语音识别服务启动完成
2025-07-30 17:04:47,150 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:04:47,159 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:04:50,344 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:04:50,349 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:04:50,350 - root - INFO - 语音识别服务启动完成
2025-07-30 17:06:20,725 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:06:20,725 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:06:22,606 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:06:22,607 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:06:22,607 - root - INFO - 语音识别服务启动完成
2025-07-30 17:06:26,579 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:06:26,580 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:06:28,348 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:06:28,348 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:06:28,348 - root - INFO - 语音识别服务启动完成
2025-07-30 17:07:50,334 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:07:50,334 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:07:52,544 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:07:52,545 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:07:52,545 - root - INFO - 语音识别服务启动完成
2025-07-30 17:09:10,357 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 56189
2025-07-30 17:09:10,366 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppv3wxl09.webm, 大小: 56189 bytes
2025-07-30 17:09:14,027 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 17:09:14,027 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppv3wxl09.webm': Format not recognised.
2025-07-30 17:09:14,027 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppv3wxl09.webm
2025-07-30 17:09:14,028 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 17:09:14,036 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 17:09:14,036 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 17:09:14,038 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 17:09:14,039 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 17:09:14,043 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp5hf1fpxh.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 17:09:14,443 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 17:09:14,443 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 17:09:44,828 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:09:44,829 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:09:48,938 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:09:48,939 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:09:48,939 - root - INFO - 语音识别服务启动完成
2025-07-30 17:10:04,790 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 68747
2025-07-30 17:10:04,800 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpgpai524r.webm, 大小: 68747 bytes
2025-07-30 17:10:07,318 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 17:10:07,318 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgpai524r.webm': Format not recognised.
2025-07-30 17:10:07,318 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpgpai524r.webm
2025-07-30 17:10:07,319 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 17:10:07,323 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 17:10:07,324 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 17:10:07,324 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 17:10:07,325 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 17:10:07,332 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpw5bzu9xn.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 17:10:07,735 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 17:10:07,735 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 17:19:35,758 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 76475
2025-07-30 17:19:35,790 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp71hglv9z.webm, 大小: 76475 bytes
2025-07-30 17:19:35,876 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 17:19:35,881 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp71hglv9z.webm': Format not recognised.
2025-07-30 17:19:35,887 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp71hglv9z.webm
2025-07-30 17:19:35,892 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 17:19:35,928 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 17:19:35,928 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 17:19:35,938 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 17:19:35,943 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 17:19:35,960 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpxj49577y.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 17:19:36,626 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 17:19:36,627 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 17:27:25,073 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:27:25,078 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:28:16,054 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:28:16,054 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:28:16,055 - root - INFO - 语音识别服务启动完成
2025-07-30 17:28:41,698 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 60053
2025-07-30 17:28:41,707 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppg5y1f3i.webm, 大小: 60053 bytes
2025-07-30 17:28:45,290 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 17:28:45,290 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppg5y1f3i.webm': Format not recognised.
2025-07-30 17:28:45,291 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppg5y1f3i.webm
2025-07-30 17:28:45,291 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 17:28:45,296 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 17:28:45,298 - src.speech_recognizer - INFO - 尝试最后的备用方案...
2025-07-30 17:28:45,298 - src.speech_recognizer - WARNING - 使用静音数据作为备用方案
2025-07-30 17:28:45,298 - src.speech_recognizer - INFO - 音频信息: 时长=1.00秒, 原始采样率=16000Hz, 数据长度=16000
2025-07-30 17:28:45,303 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpmfrhwu4a.wav, 大小: 32044 bytes, 采样率: 16000Hz
2025-07-30 17:28:45,790 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-30 17:28:45,790 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-30 17:33:12,305 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:33:12,306 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:33:16,430 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:33:16,431 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:33:16,431 - root - INFO - 语音识别服务启动完成
2025-07-30 17:33:21,395 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:33:21,395 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:41:50,391 - src.speech_recognizer - WARNING - 未找到FFmpeg，WebM格式支持可能受限
2025-07-30 17:41:50,393 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:41:50,393 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:41:50,394 - root - INFO - 语音识别服务启动完成
2025-07-30 17:42:29,417 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:42:29,418 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:42:32,018 - src.speech_recognizer - WARNING - 未找到FFmpeg，WebM格式支持可能受限
2025-07-30 17:42:32,018 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:42:32,019 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:42:32,019 - root - INFO - 语音识别服务启动完成
2025-07-30 17:43:57,404 - src.speech_recognizer - INFO - 处理音频文件: tmpkww8d1h6.webm, 大小: 32
2025-07-30 17:43:57,424 - src.speech_recognizer - ERROR - 音频预处理失败: 音频文件太小(32 bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。
2025-07-30 17:43:57,427 - src.speech_recognizer - ERROR - 语音识别失败: 音频文件太小(32 bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。
Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 125, in recognize
    processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 176, in _preprocess_audio
    raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。")
ValueError: 音频文件太小(32 bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。
2025-07-30 17:43:57,444 - root - ERROR - 语音识别失败: 音频文件太小(32 bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。
Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 125, in recognize
    processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 176, in _preprocess_audio
    raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。")
ValueError: 音频文件太小(32 bytes)，可能录音失败或文件损坏。请检查前端录音功能是否正常工作。
2025-07-30 17:51:22,609 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:51:22,611 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:51:25,596 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-30 17:51:25,598 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-30 17:51:25,653 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-30 17:51:25,653 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:51:25,654 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:51:25,654 - root - INFO - 语音识别服务启动完成
2025-07-30 17:53:40,597 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-30 17:53:40,598 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-30 17:53:40,647 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-30 17:53:40,648 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:53:40,648 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:53:40,648 - root - INFO - 语音识别服务启动完成
2025-07-30 17:58:34,076 - src.speech_recognizer - INFO - 处理音频文件: tmps71bw2hu.webm, 大小: 20950
2025-07-30 17:58:34,094 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp2jibsqbc.webm, 大小: 20950 bytes
2025-07-30 17:58:38,115 - src.speech_recognizer - ERROR - librosa读取音频失败: 
2025-07-30 17:58:38,116 - src.speech_recognizer - ERROR - soundfile读取音频也失败: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.
2025-07-30 17:58:38,116 - src.speech_recognizer - INFO - 尝试使用pydub处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp2jibsqbc.webm
2025-07-30 17:58:38,116 - src.speech_recognizer - INFO - 检测到文件格式: .webm
2025-07-30 17:58:38,125 - src.speech_recognizer - ERROR - pydub处理音频也失败: [WinError 2] 系统找不到指定的文件。
2025-07-30 17:58:38,125 - src.speech_recognizer - ERROR - 音频预处理失败: WebM格式处理失败，需要安装FFmpeg。文件大小: 20950 bytes。错误详情: librosa(), soundfile(Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.), pydub([WinError 2] 系统找不到指定的文件。)
2025-07-30 17:58:38,126 - src.speech_recognizer - ERROR - 语音识别失败: WebM格式处理失败，需要安装FFmpeg。文件大小: 20950 bytes。错误详情: librosa(), soundfile(Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.), pydub([WinError 2] 系统找不到指定的文件。)
Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 176, in load
    y, sr_native = __soundfile_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 209, in __soundfile_load
    context = sf.SoundFile(path)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 197, in _preprocess_audio
    audio_data, sample_rate = librosa.load(file_path, sr=None, mono=True)
                              ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 184, in load
    y, sr_native = __audioread_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\util\decorators.py", line 63, in __wrapper
    return func(*args, **kwargs)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 240, in __audioread_load
    reader = audioread.audio_open(path)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\audioread\__init__.py", line 132, in audio_open
    raise NoBackendError()
audioread.exceptions.NoBackendError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 202, in _preprocess_audio
    audio_data, sample_rate = sf.read(file_path)
                              ~~~~~~~^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 305, in read
    with SoundFile(file, 'r', samplerate, channels,
         ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   subtype, endian, format, closefd) as f:
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 218, in _preprocess_audio
    audio_segment = AudioSegment.from_file(file_path, format="webm")
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\pydub\audio_segment.py", line 728, in from_file
    info = mediainfo_json(orig_file, read_ahead_limit=read_ahead_limit)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\pydub\utils.py", line 274, in mediainfo_json
    res = Popen(command, stdin=stdin_parameter, stdout=PIPE, stderr=PIPE)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\subprocess.py", line 1039, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                        pass_fds, cwd, env,
                        ^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
                        gid, gids, uid, umask,
                        ^^^^^^^^^^^^^^^^^^^^^^
                        start_new_session, process_group)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\subprocess.py", line 1554, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
                             # no special security
                             ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
                             cwd,
                             ^^^^
                             startupinfo)
                             ^^^^^^^^^^^^
FileNotFoundError: [WinError 2] 系统找不到指定的文件。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 140, in recognize
    processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 251, in _preprocess_audio
    raise ValueError(f"WebM格式处理失败，需要安装FFmpeg。文件大小: {file_size} bytes。错误详情: librosa({e}), soundfile({e2}), pydub({e3})")
ValueError: WebM格式处理失败，需要安装FFmpeg。文件大小: 20950 bytes。错误详情: librosa(), soundfile(Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.), pydub([WinError 2] 系统找不到指定的文件。)
2025-07-30 17:58:38,209 - root - ERROR - 语音识别失败: WebM格式处理失败，需要安装FFmpeg。文件大小: 20950 bytes。错误详情: librosa(), soundfile(Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.), pydub([WinError 2] 系统找不到指定的文件。)
Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 176, in load
    y, sr_native = __soundfile_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 209, in __soundfile_load
    context = sf.SoundFile(path)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 197, in _preprocess_audio
    audio_data, sample_rate = librosa.load(file_path, sr=None, mono=True)
                              ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 184, in load
    y, sr_native = __audioread_load(path, offset, duration, dtype)
                   ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\util\decorators.py", line 63, in __wrapper
    return func(*args, **kwargs)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\librosa\core\audio.py", line 240, in __audioread_load
    reader = audioread.audio_open(path)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\audioread\__init__.py", line 132, in audio_open
    raise NoBackendError()
audioread.exceptions.NoBackendError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 202, in _preprocess_audio
    audio_data, sample_rate = sf.read(file_path)
                              ~~~~~~~^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 305, in read
    with SoundFile(file, 'r', samplerate, channels,
         ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                   subtype, endian, format, closefd) as f:
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 690, in __init__
    self._file = self._open(file, mode_int, closefd)
                 ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\soundfile.py", line 1265, in _open
    raise LibsndfileError(err, prefix="Error opening {0!r}: ".format(self.name))
soundfile.LibsndfileError: Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 218, in _preprocess_audio
    audio_segment = AudioSegment.from_file(file_path, format="webm")
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\pydub\audio_segment.py", line 728, in from_file
    info = mediainfo_json(orig_file, read_ahead_limit=read_ahead_limit)
  File "D:\Git\CRM\python\report_server\.venv\Lib\site-packages\pydub\utils.py", line 274, in mediainfo_json
    res = Popen(command, stdin=stdin_parameter, stdout=PIPE, stderr=PIPE)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\subprocess.py", line 1039, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                        pass_fds, cwd, env,
                        ^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
                        gid, gids, uid, umask,
                        ^^^^^^^^^^^^^^^^^^^^^^
                        start_new_session, process_group)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.13.5-windows-x86_64-none\Lib\subprocess.py", line 1554, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
                             # no special security
                             ^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
                             cwd,
                             ^^^^
                             startupinfo)
                             ^^^^^^^^^^^^
FileNotFoundError: [WinError 2] 系统找不到指定的文件。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Git\CRM\python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 140, in recognize
    processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\python\report_server\src\speech_recognizer.py", line 251, in _preprocess_audio
    raise ValueError(f"WebM格式处理失败，需要安装FFmpeg。文件大小: {file_size} bytes。错误详情: librosa({e}), soundfile({e2}), pydub({e3})")
ValueError: WebM格式处理失败，需要安装FFmpeg。文件大小: 20950 bytes。错误详情: librosa(), soundfile(Error opening 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2jibsqbc.webm': Format not recognised.), pydub([WinError 2] 系统找不到指定的文件。)
2025-07-30 17:59:20,791 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:59:20,799 - root - INFO - 语音识别服务关闭完成
2025-07-30 17:59:27,156 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-30 17:59:27,157 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-30 17:59:27,159 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-30 17:59:27,258 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-30 17:59:27,259 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 17:59:27,259 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 17:59:27,260 - root - INFO - 语音识别服务启动完成
2025-07-30 17:59:30,150 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 17:59:30,150 - root - INFO - 语音识别服务关闭完成
2025-07-30 18:03:46,803 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-30 18:03:46,804 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-30 18:03:46,804 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-30 18:03:46,857 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-30 18:03:46,858 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 18:03:46,858 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 18:03:46,858 - root - INFO - 语音识别服务启动完成
2025-07-30 18:04:33,045 - src.speech_recognizer - INFO - 处理音频文件: test.webm, 大小: 20950
2025-07-30 18:04:33,053 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppum7xfz6.webm, 大小: 20950 bytes
2025-07-30 18:04:38,134 - src.speech_recognizer - INFO - 音频信息: 时长=2.00秒, 原始采样率=48000Hz, 数据长度=96000
2025-07-30 18:04:38,140 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 18:04:38,154 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpt3ie80zv.wav, 大小: 64044 bytes, 采样率: 16000Hz
2025-07-30 18:04:38,676 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 1980, 'text': '嗯。', 'words': [{'begin_time': 0, 'end_time': 1980, 'text': '嗯', 'punctuation': '。'}]}]}
2025-07-30 18:05:32,872 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 18:05:32,905 - root - INFO - 语音识别服务关闭完成
2025-07-30 18:05:42,847 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-30 18:05:42,848 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-30 18:05:42,848 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-30 18:05:42,895 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-30 18:05:42,895 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 18:05:42,895 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 18:05:42,895 - root - INFO - 语音识别服务启动完成
2025-07-30 18:06:07,008 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 65849
2025-07-30 18:06:07,016 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppb_pcmeb.webm, 大小: 65849 bytes
2025-07-30 18:06:10,967 - src.speech_recognizer - INFO - 音频信息: 时长=4.08秒, 原始采样率=48000Hz, 数据长度=195840
2025-07-30 18:06:10,973 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 18:06:10,984 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp7xncpfps.wav, 大小: 130604 bytes, 采样率: 16000Hz
2025-07-30 18:06:11,590 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 240, 'end_time': 1040, 'text': '哎。', 'words': [{'begin_time': 240, 'end_time': 1040, 'text': '哎', 'punctuation': '。'}]}, {'begin_time': 2190, 'end_time': 4060, 'text': '哎呀，我。', 'words': [{'begin_time': 2190, 'end_time': 3436, 'text': '哎呀', 'punctuation': '，'}, {'begin_time': 3436, 'end_time': 4060, 'text': '我', 'punctuation': '。'}]}]}
2025-07-30 18:06:38,442 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 63917
2025-07-30 18:06:38,446 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpbe8o3hre.webm, 大小: 63917 bytes
2025-07-30 18:06:38,550 - src.speech_recognizer - INFO - 音频信息: 时长=3.96秒, 原始采样率=48000Hz, 数据长度=190080
2025-07-30 18:06:38,553 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-30 18:06:38,556 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpocvi85xy.wav, 大小: 126764 bytes, 采样率: 16000Hz
2025-07-30 18:06:39,092 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 490, 'text': '所以。', 'words': [{'begin_time': 0, 'end_time': 490, 'text': '所以', 'punctuation': '。'}]}, {'begin_time': 1260, 'end_time': 3940, 'text': '语音输入测试。', 'words': [{'begin_time': 1260, 'end_time': 2153, 'text': '语音', 'punctuation': ''}, {'begin_time': 2153, 'end_time': 3046, 'text': '输入', 'punctuation': ''}, {'begin_time': 3046, 'end_time': 3940, 'text': '测试', 'punctuation': '。'}]}]}
2025-07-30 18:07:02,554 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-30 18:07:02,554 - root - INFO - 语音识别服务关闭完成
2025-07-30 18:07:05,909 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-30 18:07:05,909 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-30 18:07:05,910 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-30 18:07:05,958 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-30 18:07:05,959 - src.speech_recognizer - INFO - API连接测试通过
2025-07-30 18:07:05,959 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-30 18:07:05,961 - root - INFO - 语音识别服务启动完成
2025-07-31 09:08:01,932 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 09:08:01,938 - root - INFO - 语音识别服务关闭完成
2025-07-31 09:08:16,392 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 09:08:16,393 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 09:08:16,394 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 09:08:17,015 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 09:08:17,015 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 09:08:17,015 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 09:08:17,015 - root - INFO - 语音识别服务启动完成
2025-07-31 09:48:30,742 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 23345
2025-07-31 09:48:30,850 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmptga4n76w.webm, 大小: 23345 bytes
2025-07-31 09:48:45,566 - src.speech_recognizer - INFO - 音频信息: 时长=1.44秒, 原始采样率=48000Hz, 数据长度=69120
2025-07-31 09:48:45,572 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 09:48:45,579 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpkvx_5ebk.wav, 大小: 46124 bytes, 采样率: 16000Hz
2025-07-31 09:48:46,163 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-31 09:48:46,164 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-31 09:49:23,536 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 165347
2025-07-31 09:49:23,540 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpvr20f7pf.webm, 大小: 165347 bytes
2025-07-31 09:49:23,673 - src.speech_recognizer - INFO - 音频信息: 时长=10.26秒, 原始采样率=48000Hz, 数据长度=492480
2025-07-31 09:49:23,678 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 09:49:23,683 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpd0gqk6ue.wav, 大小: 328364 bytes, 采样率: 16000Hz
2025-07-31 09:49:24,686 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 920, 'text': '同样是。', 'words': [{'begin_time': 0, 'end_time': 460, 'text': '同样', 'punctuation': ''}, {'begin_time': 460, 'end_time': 920, 'text': '是', 'punctuation': '。'}]}, {'begin_time': 2000, 'end_time': 5740, 'text': '哦，你这个青竹坝成了简陋轿子。', 'words': [{'begin_time': 2000, 'end_time': 2287, 'text': '哦', 'punctuation': '，'}, {'begin_time': 2287, 'end_time': 2862, 'text': '你这', 'punctuation': ''}, {'begin_time': 2862, 'end_time': 3437, 'text': '个青', 'punctuation': ''}, {'begin_time': 3437, 'end_time': 4012, 'text': '竹坝', 'punctuation': ''}, {'begin_time': 4012, 'end_time': 4587, 'text': '成了', 'punctuation': ''}, {'begin_time': 4587, 'end_time': 5162, 'text': '简陋', 'punctuation': ''}, {'begin_time': 5162, 'end_time': 5740, 'text': '轿子', 'punctuation': '。'}]}, {'begin_time': 6200, 'end_time': 10240, 'text': '叫上坐着的却是一个满脸黑麻子。还。', 'words': [{'begin_time': 6200, 'end_time': 6738, 'text': '叫上', 'punctuation': ''}, {'begin_time': 6738, 'end_time': 7276, 'text': '坐着', 'punctuation': ''}, {'begin_time': 7276, 'end_time': 7814, 'text': '的却', 'punctuation': ''}, {'begin_time': 7814, 'end_time': 8352, 'text': '是一', 'punctuation': ''}, {'begin_time': 8352, 'end_time': 8890, 'text': '个满', 'punctuation': ''}, {'begin_time': 8890, 'end_time': 9428, 'text': '脸黑', 'punctuation': ''}, {'begin_time': 9428, 'end_time': 9966, 'text': '麻子', 'punctuation': '。'}, {'begin_time': 9970, 'end_time': 10240, 'text': '还', 'punctuation': '。'}]}]}
2025-07-31 09:54:44,504 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 09:54:44,509 - root - INFO - 语音识别服务关闭完成
2025-07-31 09:54:48,967 - src.speech_recognizer - INFO - 找到FFmpeg: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 09:54:48,968 - src.speech_recognizer - INFO - 找到FFprobe: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 09:54:48,970 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 09:54:49,050 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 09:54:49,051 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 09:54:49,051 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 09:54:49,051 - root - INFO - 语音识别服务启动完成
2025-07-31 09:55:45,926 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 09:55:45,926 - root - INFO - 语音识别服务关闭完成
2025-07-31 09:55:51,176 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 09:55:51,178 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 09:55:51,180 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 09:55:51,225 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 09:55:51,226 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 09:55:51,226 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 09:55:51,227 - root - INFO - 语音识别服务启动完成
2025-07-31 09:57:31,786 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 09:57:31,792 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 09:57:31,793 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 09:57:31,979 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 09:57:31,980 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 09:57:31,980 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 09:57:31,981 - root - INFO - 语音识别服务启动完成
2025-07-31 10:00:31,434 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:00:31,435 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:00:31,536 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:00:31,539 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:00:35,037 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:00:35,038 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:00:35,038 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:00:35,038 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:00:35,039 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:00:35,039 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:00:35,129 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:00:35,130 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:00:35,129 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:00:35,130 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 10:00:35,130 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:00:35,130 - root - INFO - 语音识别服务启动完成
2025-07-31 10:00:35,130 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 10:00:35,131 - root - INFO - 语音识别服务启动完成
2025-07-31 10:01:10,961 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:01:10,961 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:01:10,964 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:01:10,964 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:01:14,021 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:01:14,022 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:01:14,022 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:01:14,051 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:01:14,058 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:01:14,059 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:01:14,067 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:01:14,068 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:01:14,068 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 10:01:14,069 - root - INFO - 语音识别服务启动完成
2025-07-31 10:01:14,108 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:01:14,108 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:01:14,108 - src.speech_recognizer - INFO - 语音识别器初始化成功
2025-07-31 10:01:14,109 - root - INFO - 语音识别服务启动完成
2025-07-31 10:01:25,792 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:01:25,811 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:01:25,912 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:01:25,980 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:01:29,853 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:01:29,855 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:01:29,858 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:01:29,928 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:01:29,933 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:01:29,934 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-realtime-v1
2025-07-31 10:01:29,936 - root - INFO - 语音识别服务启动完成
2025-07-31 10:01:30,139 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:01:30,141 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:01:30,144 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:01:30,252 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:01:30,253 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:01:30,256 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-realtime-v1
2025-07-31 10:01:30,256 - root - INFO - 语音识别服务启动完成
2025-07-31 10:01:39,641 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:01:39,641 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:03:40,080 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:03:40,081 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:03:40,081 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:03:40,202 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:03:40,203 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:03:40,203 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-realtime-v1
2025-07-31 10:03:40,203 - root - INFO - 语音识别服务启动完成
2025-07-31 10:05:48,231 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:05:48,231 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:06:02,988 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:06:02,992 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:06:02,993 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:06:03,067 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:06:03,068 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:06:03,069 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-v2
2025-07-31 10:06:03,069 - root - INFO - 语音识别服务启动完成
2025-07-31 10:06:44,836 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 112217
2025-07-31 10:06:44,849 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpz94tv1q8.webm, 大小: 112217 bytes
2025-07-31 10:06:50,497 - src.speech_recognizer - INFO - 音频信息: 时长=6.96秒, 原始采样率=48000Hz, 数据长度=334080
2025-07-31 10:06:50,505 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 10:06:50,512 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp0ridjrh7.wav, 大小: 222764 bytes, 采样率: 16000Hz
2025-07-31 10:06:50,512 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-realtime-v1
2025-07-31 10:06:51,432 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 0, 'end_time': 2390, 'text': '因为前原则。', 'words': [{'begin_time': 0, 'end_time': 796, 'text': '因为', 'punctuation': ''}, {'begin_time': 796, 'end_time': 1592, 'text': '前原', 'punctuation': ''}, {'begin_time': 1592, 'end_time': 2390, 'text': '则', 'punctuation': '。'}]}, {'begin_time': 3030, 'end_time': 4010, 'text': '此刻了。', 'words': [{'begin_time': 3030, 'end_time': 3520, 'text': '此刻', 'punctuation': ''}, {'begin_time': 3520, 'end_time': 4010, 'text': '了', 'punctuation': '。'}]}, {'begin_time': 4300, 'end_time': 6940, 'text': '李十五使劲晃了晃脑袋，想城。', 'words': [{'begin_time': 4300, 'end_time': 4740, 'text': '李十', 'punctuation': ''}, {'begin_time': 4740, 'end_time': 5180, 'text': '五使', 'punctuation': ''}, {'begin_time': 5180, 'end_time': 5620, 'text': '劲晃', 'punctuation': ''}, {'begin_time': 5620, 'end_time': 6060, 'text': '了晃', 'punctuation': ''}, {'begin_time': 6060, 'end_time': 6500, 'text': '脑袋', 'punctuation': '，'}, {'begin_time': 6500, 'end_time': 6940, 'text': '想城', 'punctuation': '。'}]}]}
2025-07-31 10:07:35,316 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:07:35,534 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:07:49,539 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:07:49,539 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:07:49,540 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:07:49,621 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:07:49,623 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:07:49,623 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-realtime-v2
2025-07-31 10:07:49,624 - root - INFO - 语音识别服务启动完成
2025-07-31 10:08:10,547 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 119945
2025-07-31 10:08:10,552 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp84d0x_i3.webm, 大小: 119945 bytes
2025-07-31 10:08:10,716 - src.speech_recognizer - INFO - 音频信息: 时长=7.44秒, 原始采样率=48000Hz, 数据长度=357120
2025-07-31 10:08:10,720 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 10:08:10,732 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpli2bvvb9.wav, 大小: 238124 bytes, 采样率: 16000Hz
2025-07-31 10:08:10,733 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-realtime-v1
2025-07-31 10:08:11,524 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 730, 'end_time': 1280, 'text': '嗯。', 'words': [{'begin_time': 730, 'end_time': 1280, 'text': '嗯', 'punctuation': '。'}]}, {'begin_time': 1460, 'end_time': 7420, 'text': '师傅，咱们这些弟子随你寻仙缘，你翻山越岭，所以风雪正正是。', 'words': [{'begin_time': 1460, 'end_time': 1936, 'text': '师傅', 'punctuation': '，'}, {'begin_time': 1936, 'end_time': 2373, 'text': '咱们', 'punctuation': ''}, {'begin_time': 2373, 'end_time': 2810, 'text': '这些', 'punctuation': ''}, {'begin_time': 2810, 'end_time': 3247, 'text': '弟子', 'punctuation': ''}, {'begin_time': 3247, 'end_time': 3684, 'text': '随你', 'punctuation': ''}, {'begin_time': 3684, 'end_time': 4121, 'text': '寻仙', 'punctuation': ''}, {'begin_time': 4121, 'end_time': 4558, 'text': '缘', 'punctuation': '，'}, {'begin_time': 4559, 'end_time': 4956, 'text': '你翻', 'punctuation': ''}, {'begin_time': 4956, 'end_time': 5353, 'text': '山越', 'punctuation': ''}, {'begin_time': 5353, 'end_time': 5750, 'text': '岭', 'punctuation': '，'}, {'begin_time': 5751, 'end_time': 6168, 'text': '所以', 'punctuation': ''}, {'begin_time': 6168, 'end_time': 6585, 'text': '风雪', 'punctuation': ''}, {'begin_time': 6585, 'end_time': 7002, 'text': '正正', 'punctuation': ''}, {'begin_time': 7002, 'end_time': 7420, 'text': '是', 'punctuation': '。'}]}]}
2025-07-31 10:09:50,855 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:09:50,857 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:09:56,681 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:09:56,681 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:09:56,682 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:09:56,747 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:09:56,747 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:09:56,747 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-v2
2025-07-31 10:09:56,748 - root - INFO - 语音识别服务启动完成
2025-07-31 10:10:17,776 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 144095
2025-07-31 10:10:17,779 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpns2vit11.webm, 大小: 144095 bytes
2025-07-31 10:10:17,971 - src.speech_recognizer - INFO - 音频信息: 时长=8.94秒, 原始采样率=48000Hz, 数据长度=429120
2025-07-31 10:10:17,977 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 10:10:17,982 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmphe9sxz83.wav, 大小: 286124 bytes, 采样率: 16000Hz
2025-07-31 10:10:17,984 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-realtime-v1
2025-07-31 10:10:18,822 - src.speech_recognizer - INFO - API返回的output结构: {'sentence': [{'begin_time': 690, 'end_time': 4640, 'text': '弟子冒昧想请教，师傅，究竟要如何呈现啊？', 'words': [{'begin_time': 690, 'end_time': 1096, 'text': '弟子', 'punctuation': ''}, {'begin_time': 1096, 'end_time': 1502, 'text': '冒昧', 'punctuation': ''}, {'begin_time': 1502, 'end_time': 1908, 'text': '想请', 'punctuation': ''}, {'begin_time': 1908, 'end_time': 2314, 'text': '教', 'punctuation': '，'}, {'begin_time': 2316, 'end_time': 2781, 'text': '师傅', 'punctuation': '，'}, {'begin_time': 2781, 'end_time': 3245, 'text': '究竟', 'punctuation': ''}, {'begin_time': 3245, 'end_time': 3709, 'text': '要如', 'punctuation': ''}, {'begin_time': 3709, 'end_time': 4173, 'text': '何呈', 'punctuation': ''}, {'begin_time': 4173, 'end_time': 4640, 'text': '现啊', 'punctuation': '？'}]}, {'begin_time': 5120, 'end_time': 6070, 'text': '不叫啥。', 'words': [{'begin_time': 5120, 'end_time': 5595, 'text': '不叫', 'punctuation': ''}, {'begin_time': 5595, 'end_time': 6070, 'text': '啥', 'punctuation': '。'}]}, {'begin_time': 6320, 'end_time': 8920, 'text': '节原子青抬眼皮嗯。', 'words': [{'begin_time': 6320, 'end_time': 6970, 'text': '节原', 'punctuation': ''}, {'begin_time': 6970, 'end_time': 7620, 'text': '子青', 'punctuation': ''}, {'begin_time': 7620, 'end_time': 8270, 'text': '抬眼', 'punctuation': ''}, {'begin_time': 8270, 'end_time': 8920, 'text': '皮嗯', 'punctuation': '。'}]}]}
2025-07-31 10:12:35,218 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 16583
2025-07-31 10:12:35,224 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpdb7cm15l.webm, 大小: 16583 bytes
2025-07-31 10:12:35,443 - src.speech_recognizer - INFO - 音频信息: 时长=1.02秒, 原始采样率=48000Hz, 数据长度=48960
2025-07-31 10:12:35,445 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 10:12:35,447 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpy77cwb9i.wav, 大小: 32684 bytes, 采样率: 16000Hz
2025-07-31 10:12:35,448 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-realtime-v1
2025-07-31 10:12:35,766 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-31 10:12:35,768 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-31 10:13:56,352 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 26243
2025-07-31 10:13:56,367 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp55x0jeg2.webm, 大小: 26243 bytes
2025-07-31 10:13:56,592 - src.speech_recognizer - INFO - 音频信息: 时长=1.62秒, 原始采样率=48000Hz, 数据长度=77760
2025-07-31 10:13:56,603 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 10:13:56,607 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpm9vm8coq.wav, 大小: 51884 bytes, 采样率: 16000Hz
2025-07-31 10:13:56,611 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-realtime-v1
2025-07-31 10:13:57,028 - src.speech_recognizer - INFO - API返回的output结构: None
2025-07-31 10:13:57,028 - src.speech_recognizer - WARNING - output中没有sentence字段: None
2025-07-31 10:28:53,616 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:28:53,681 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:28:53,894 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:28:53,916 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:29:00,995 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:29:00,995 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:29:00,996 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:29:01,086 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:29:01,086 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:29:01,086 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-v2
2025-07-31 10:29:01,087 - root - INFO - 语音识别服务启动完成
2025-07-31 10:29:01,928 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:29:01,929 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:29:01,929 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:29:01,984 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:29:01,985 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:29:01,985 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-realtime-v1
2025-07-31 10:29:01,985 - root - INFO - 语音识别服务启动完成
2025-07-31 10:30:17,969 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:30:17,971 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 10:30:18,055 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:30:18,057 - root - INFO - 语音识别服务关闭完成
2025-07-31 10:30:22,205 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:30:22,206 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:30:22,207 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:30:22,263 - src.speech_recognizer - INFO - 使用配置的FFmpeg路径: D:\ffmpeg\bin\ffmpeg.exe
2025-07-31 10:30:22,263 - src.speech_recognizer - INFO - 使用配置的FFprobe路径: D:\ffmpeg\bin\ffprobe.exe
2025-07-31 10:30:22,265 - src.speech_recognizer - INFO - 添加FFmpeg目录到PATH: D:\ffmpeg\bin
2025-07-31 10:30:22,265 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:30:22,268 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:30:22,269 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-realtime-v1
2025-07-31 10:30:22,270 - root - INFO - 语音识别服务启动完成
2025-07-31 10:30:22,321 - src.speech_recognizer - INFO - FFmpeg测试成功
2025-07-31 10:30:22,322 - src.speech_recognizer - INFO - API连接测试通过
2025-07-31 10:30:22,322 - src.speech_recognizer - INFO - 语音识别器初始化成功，使用模型: paraformer-v2
2025-07-31 10:30:22,322 - root - INFO - 语音识别服务启动完成
2025-07-31 11:03:30,679 - src.speech_recognizer - INFO - 语音识别器清理完成
2025-07-31 11:03:30,686 - root - INFO - 语音识别服务关闭完成
2025-07-31 11:18:44,783 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 178871
2025-07-31 11:18:44,855 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp_98_5b31.webm, 大小: 178871 bytes
2025-07-31 11:18:52,312 - src.speech_recognizer - INFO - 音频信息: 时长=11.10秒, 原始采样率=48000Hz, 数据长度=532800
2025-07-31 11:18:52,322 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 11:18:52,333 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpcxcwfpo_.wav, 大小: 355244 bytes, 采样率: 16000Hz
2025-07-31 11:18:52,334 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-v2
2025-07-31 11:18:52,698 - dashscope - ERROR - Request failed, request_id: b3e7691161c54366ac8cc0fdc7ee787a, http_code: 44 error_name: ModelNotFound, error_message: Model not found (paraformer-v2)!
2025-07-31 11:18:52,700 - dashscope - ERROR - {"status_code": 44, "request_id": "b3e7691161c54366ac8cc0fdc7ee787a", "code": "ModelNotFound", "message": "Model not found (paraformer-v2)!", "output": null, "usage": null}
2025-07-31 11:18:52,702 - src.speech_recognizer - ERROR - API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:18:52,702 - src.speech_recognizer - ERROR - 调用阿里百炼API失败: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:18:52,703 - src.speech_recognizer - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:18:52,709 - root - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:23:39,548 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 21413
2025-07-31 11:23:39,553 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmppwij2z6w.webm, 大小: 21413 bytes
2025-07-31 11:23:39,711 - src.speech_recognizer - INFO - 音频信息: 时长=1.32秒, 原始采样率=48000Hz, 数据长度=63360
2025-07-31 11:23:39,713 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 11:23:39,716 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpobbd6o84.wav, 大小: 42284 bytes, 采样率: 16000Hz
2025-07-31 11:23:39,719 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-v2
2025-07-31 11:23:39,919 - dashscope - ERROR - Request failed, request_id: 2778e29552894d3093e4834dc7e007b5, http_code: 44 error_name: ModelNotFound, error_message: Model not found (paraformer-v2)!
2025-07-31 11:23:39,921 - dashscope - ERROR - {"status_code": 44, "request_id": "2778e29552894d3093e4834dc7e007b5", "code": "ModelNotFound", "message": "Model not found (paraformer-v2)!", "output": null, "usage": null}
2025-07-31 11:23:39,921 - src.speech_recognizer - ERROR - API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:23:39,921 - src.speech_recognizer - ERROR - 调用阿里百炼API失败: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:23:39,921 - src.speech_recognizer - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:23:39,924 - root - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:29:32,088 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 21413
2025-07-31 11:29:32,113 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmpzwrja1aa.webm, 大小: 21413 bytes
2025-07-31 11:29:32,362 - src.speech_recognizer - INFO - 音频信息: 时长=1.32秒, 原始采样率=48000Hz, 数据长度=63360
2025-07-31 11:29:32,376 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 11:29:32,384 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmp40t88gba.wav, 大小: 42284 bytes, 采样率: 16000Hz
2025-07-31 11:29:32,388 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-v2
2025-07-31 11:29:32,657 - dashscope - ERROR - Request failed, request_id: d6b76b4c16534bf1a65791beddab57b4, http_code: 44 error_name: ModelNotFound, error_message: Model not found (paraformer-v2)!
2025-07-31 11:29:32,661 - dashscope - ERROR - {"status_code": 44, "request_id": "d6b76b4c16534bf1a65791beddab57b4", "code": "ModelNotFound", "message": "Model not found (paraformer-v2)!", "output": null, "usage": null}
2025-07-31 11:29:32,663 - src.speech_recognizer - ERROR - API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:29:32,663 - src.speech_recognizer - ERROR - 调用阿里百炼API失败: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:29:32,664 - src.speech_recognizer - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:29:32,672 - root - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:30:36,396 - src.speech_recognizer - INFO - 处理音频文件: recording.webm, 大小: 46529
2025-07-31 11:30:36,404 - src.speech_recognizer - INFO - 开始预处理音频文件: C:\Users\<USER>\AppData\Local\Temp\tmp1usma3_1.webm, 大小: 46529 bytes
2025-07-31 11:30:36,546 - src.speech_recognizer - INFO - 音频信息: 时长=2.88秒, 原始采样率=48000Hz, 数据长度=138240
2025-07-31 11:30:36,550 - src.speech_recognizer - INFO - 重采样到 16000Hz
2025-07-31 11:30:36,552 - src.speech_recognizer - INFO - 音频预处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpbqauwkp7.wav, 大小: 92204 bytes, 采样率: 16000Hz
2025-07-31 11:30:36,555 - src.speech_recognizer - INFO - 使用百炼模型: paraformer-v2
2025-07-31 11:30:36,769 - dashscope - ERROR - Request failed, request_id: c73d839a6191493e844603c7025f1d79, http_code: 44 error_name: ModelNotFound, error_message: Model not found (paraformer-v2)!
2025-07-31 11:30:36,771 - dashscope - ERROR - {"status_code": 44, "request_id": "c73d839a6191493e844603c7025f1d79", "code": "ModelNotFound", "message": "Model not found (paraformer-v2)!", "output": null, "usage": null}
2025-07-31 11:30:36,772 - src.speech_recognizer - ERROR - API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:30:36,772 - src.speech_recognizer - ERROR - 调用阿里百炼API失败: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:30:36,772 - src.speech_recognizer - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
2025-07-31 11:30:36,775 - root - ERROR - 语音识别失败: API调用失败: 44, Model not found (paraformer-v2)!
Traceback (most recent call last):
  File "D:\Git\CRM\Python\report_server\main.py", line 100, in recognize_speech
    result = await speech_recognizer.recognize(audio)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 173, in recognize
    recognition_result = await self._call_dashscope_api(processed_file_path)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Git\CRM\Python\report_server\src\speech_recognizer.py", line 358, in _call_dashscope_api
    raise RuntimeError(error_msg)
RuntimeError: API调用失败: 44, Model not found (paraformer-v2)!
